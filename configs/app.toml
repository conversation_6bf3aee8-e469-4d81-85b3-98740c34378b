# Pixiu 应用主配置文件
# 这是一个 TOML 格式的配置文件，包含所有模块的配置

[log]
# 日志级别：debug, info, warn, error
level = "info"

# 日志文件路径，留空则输出到标准输出
file_path = "logs/app.log"

[database]
# 数据库驱动：sqlite, mysql, postgres
driver = "sqlite"

# 数据库连接参数
host = "localhost"
port = 0                    # SQLite 不需要端口
database = "pixiu.db"       # SQLite 数据库文件路径
username = ""               # SQLite 不需要用户名
password = ""               # SQLite 不需要密码

# SSL 和字符集配置（主要用于 MySQL 和 PostgreSQL）
ssl_mode = "disable"
charset = "utf8mb4"

# 连接池配置
max_open_conns = 25         # 最大打开连接数
max_idle_conns = 5          # 最大空闲连接数
conn_max_lifetime = 300     # 连接最大生存时间（秒）
conn_max_idle_time = 60     # 连接最大空闲时间（秒）

[server]
# HTTP 服务器配置
host = "localhost"
port = 8080
read_timeout = 30           # 读取超时（秒）
write_timeout = 30          # 写入超时（秒）
framework = "gin"           # HTTP 框架：gin, fiber, net/http
