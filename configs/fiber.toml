# Pixiu Fiber 框架配置示例

[log]
level = "info"
file_path = "logs/fiber.log"

[database]
driver = "sqlite"
host = "localhost"
port = 0
database = "pixiu_fiber.db"
username = ""
password = ""
ssl_mode = "disable"
charset = "utf8mb4"
max_open_conns = 25
max_idle_conns = 5
conn_max_lifetime = 300
conn_max_idle_time = 60

[server]
# 使用 Fiber 框架
host = "localhost"
port = 8081
read_timeout = 30
write_timeout = 30
framework = "fiber"
