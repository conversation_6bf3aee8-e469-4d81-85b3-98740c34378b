# Pixiu MySQL 数据库配置示例
# 注意：使用 MySQL 需要先安装驱动：go get github.com/go-sql-driver/mysql

[log]
level = "info"
file_path = "logs/app.log"

[database]
# MySQL 数据库配置
driver = "mysql"
host = "localhost"
port = 3306
database = "pixiu_db"
username = "pixiu_user"
password = "your_password_here"

# MySQL 特定配置
charset = "utf8mb4"
ssl_mode = "disable"        # MySQL 中此字段被忽略

# 连接池配置
max_open_conns = 50
max_idle_conns = 10
conn_max_lifetime = 600     # 10分钟
conn_max_idle_time = 120    # 2分钟
