# Pixiu PostgreSQL 数据库配置示例
# 注意：使用 PostgreSQL 需要先安装驱动：go get github.com/lib/pq

[log]
level = "info"
file_path = "logs/app.log"

[database]
# PostgreSQL 数据库配置
driver = "postgres"
host = "localhost"
port = 5432
database = "pixiu_db"
username = "pixiu_user"
password = "your_password_here"

# PostgreSQL 特定配置
ssl_mode = "disable"        # disable, require, verify-ca, verify-full
charset = "utf8"            # PostgreSQL 中此字段被忽略

# 连接池配置
max_open_conns = 50
max_idle_conns = 10
conn_max_lifetime = 600     # 10分钟
conn_max_idle_time = 120    # 2分钟
