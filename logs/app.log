time=2025-05-29T16:22:56.259+08:00 level=INFO msg=应用初始化成功 config_file=configs/database.toml log_level=info log_output=logs/app.log database_driver=sqlite database_name=pixiu.db
time=2025-05-29T16:22:56.263+08:00 level=INFO msg=示例表创建成功
time=2025-05-29T16:22:56.272+08:00 level=INFO msg=示例数据插入成功
time=2025-05-29T16:22:56.272+08:00 level=INFO msg=应用资源已清理
time=2025-05-29T16:25:53.834+08:00 level=INFO msg=应用初始化成功 config_file=configs/app.toml log_level=info log_output=logs/app.log database_driver=sqlite database_name=pixiu.db
time=2025-05-29T16:25:53.834+08:00 level=INFO msg=示例表创建成功
time=2025-05-29T16:25:53.852+08:00 level=INFO msg=示例数据插入成功
time=2025-05-29T16:25:53.852+08:00 level=INFO msg=应用资源已清理
time=2025-05-29T16:34:44.429+08:00 level=INFO msg=应用初始化成功 config_file=configs/app.toml log_level=info log_output=logs/app.log database_driver=sqlite database_name=pixiu.db server_framework=gin server_address=localhost:8080
time=2025-05-29T16:34:44.429+08:00 level=INFO msg="启动 HTTP 服务器" address=localhost:8080
time=2025-05-29T16:34:49.656+08:00 level=INFO msg=收到停止信号，正在关闭服务器
time=2025-05-29T16:34:49.656+08:00 level=INFO msg=应用资源已清理
time=2025-05-29T16:35:00.765+08:00 level=INFO msg=应用初始化成功 config_file=configs/app.toml log_level=info log_output=logs/app.log database_driver=sqlite database_name=pixiu.db server_framework=gin server_address=localhost:8080
time=2025-05-29T16:35:00.765+08:00 level=INFO msg="启动 HTTP 服务器" address=localhost:8080
time=2025-05-29T16:36:39.183+08:00 level=INFO msg=应用初始化成功 config_file=configs/app.toml log_level=info log_output=logs/app.log database_driver=sqlite database_name=pixiu.db server_framework=gin server_address=localhost:8080
time=2025-05-29T16:36:39.183+08:00 level=INFO msg=这是一条信息日志
time=2025-05-29T16:36:39.183+08:00 level=WARN msg=这是一条警告日志
time=2025-05-29T16:36:39.183+08:00 level=ERROR msg=这是一条错误日志
time=2025-05-29T16:36:49.235+08:00 level=INFO msg=应用初始化成功 config_file=configs/app.toml log_level=info log_output=logs/app.log database_driver=sqlite database_name=pixiu.db server_framework=gin server_address=localhost:8080
time=2025-05-29T16:36:49.235+08:00 level=INFO msg=示例表创建成功
time=2025-05-29T16:36:49.253+08:00 level=INFO msg=示例数据插入成功
time=2025-05-29T16:36:49.253+08:00 level=INFO msg=应用资源已清理
time=2025-05-29T17:07:17.363+08:00 level=INFO msg=应用初始化成功 config_file=configs/app.toml log_level=info log_output=logs/app.log database_driver=sqlite database_name=pixiu.db server_framework=gin server_address=localhost:8080
time=2025-05-29T17:07:17.363+08:00 level=INFO msg=应用资源已清理
time=2025-05-29T17:34:26.632+08:00 level=WARN msg=创建用户表失败 error="创建索引失败: SQL logic error: no such column: username (1)"
time=2025-05-29T17:34:26.632+08:00 level=INFO msg=应用初始化成功 config_file=configs/app.toml log_level=info log_output=logs/app.log database_driver=sqlite database_name=pixiu.db server_framework=gin server_address=localhost:8080
time=2025-05-29T17:34:26.632+08:00 level=INFO msg=启动认证服务器 address=localhost:8080
time=2025-05-29T20:21:00.070+08:00 level=WARN msg=创建用户表失败 error="创建索引失败: SQL logic error: no such column: username (1)"
time=2025-05-29T20:21:00.070+08:00 level=INFO msg=应用初始化成功 config_file=configs/app.toml log_level=info log_output=logs/app.log database_driver=sqlite database_name=pixiu.db server_framework=gin server_address=localhost:8080
time=2025-05-29T20:21:00.070+08:00 level=INFO msg=启动认证服务器 address=localhost:8080
time=2025-05-29T20:21:05.046+08:00 level=INFO msg=收到停止信号，正在关闭服务器
time=2025-05-29T20:21:05.047+08:00 level=INFO msg=应用资源已清理
