# Auth 认证模块

Pixiu 项目的独立认证模块，提供完整的用户认证和权限管理功能。

## 📁 模块结构

```
backend/auth/
├── README.md          # 本文件
├── config.go          # 认证配置
├── auth.go            # 认证服务核心
├── user.go            # 用户模型和数据库操作
└── middleware.go      # 认证中间件
```

## 🚀 功能特性

- ✅ **JWT 认证**：安全的 JWT 令牌生成和验证
- ✅ **用户管理**：完整的用户 CRUD 操作
- ✅ **密码安全**：bcrypt 加密和密码强度验证
- ✅ **会话管理**：用户会话创建、验证和清理
- ✅ **多框架支持**：Gin、Fiber、net/http 中间件
- ✅ **角色权限**：基于角色的访问控制（RBAC）
- ✅ **数据库迁移**：自动创建用户相关表

## 📦 快速开始

### 1. 创建认证服务

```go
package main

import (
    "database/sql"
    "pixiu/backend/auth"
)

func main() {
    // 数据库连接
    db, err := sql.Open("sqlite", "app.db")
    if err != nil {
        panic(err)
    }
    defer db.Close()

    // 创建认证配置
    config := auth.NewDefaultConfig()
    config.JWTSecret = "your-secret-key"

    // 创建认证服务
    authService, err := auth.NewService(db, config)
    if err != nil {
        panic(err)
    }

    // 初始化数据库表
    if err := authService.InitializeTables(); err != nil {
        panic(err)
    }
}
```

### 2. 用户注册和登录

```go
// 用户注册
registerReq := &auth.CreateUserRequest{
    Username: "testuser",
    Email:    "<EMAIL>",
    Password: "Test123!@#",
    Role:     "user",
}

user, err := authService.Register(registerReq)
if err != nil {
    // 处理错误
}

// 用户登录
loginReq := &auth.LoginRequest{
    Username: "testuser",
    Password: "Test123!@#",
}

loginResp, err := authService.Login(loginReq)
if err != nil {
    // 处理错误
}

// 获取令牌
token := loginResp.Token
```

### 3. 使用认证中间件

#### Gin 框架

```go
import (
    "github.com/gin-gonic/gin"
    "pixiu/backend/auth"
)

func setupRoutes(authService *auth.Service) *gin.Engine {
    r := gin.Default()

    // 公开路由
    r.POST("/auth/register", registerHandler)
    r.POST("/auth/login", loginHandler)

    // 需要认证的路由
    protected := r.Group("/api")
    protected.Use(authService.GinAuthMiddleware())
    {
        protected.GET("/profile", getProfileHandler)
        protected.PUT("/profile", updateProfileHandler)
    }

    // 需要管理员权限的路由
    admin := r.Group("/admin")
    admin.Use(authService.GinAuthMiddleware())
    admin.Use(authService.GinRoleMiddleware("admin"))
    {
        admin.GET("/users", getUsersHandler)
    }

    return r
}
```

#### Fiber 框架

```go
import (
    "github.com/gofiber/fiber/v2"
    "pixiu/backend/auth"
)

func setupRoutes(authService *auth.Service) *fiber.App {
    app := fiber.New()

    // 公开路由
    app.Post("/auth/register", registerHandler)
    app.Post("/auth/login", loginHandler)

    // 需要认证的路由
    api := app.Group("/api")
    api.Use(authService.FiberAuthMiddleware())
    api.Get("/profile", getProfileHandler)
    api.Put("/profile", updateProfileHandler)

    // 需要管理员权限的路由
    admin := app.Group("/admin")
    admin.Use(authService.FiberAuthMiddleware())
    admin.Use(authService.FiberRoleMiddleware("admin"))
    admin.Get("/users", getUsersHandler)

    return app
}
```

### 4. 获取当前用户

```go
// 在 Gin 处理器中
func getProfileHandler(c *gin.Context) {
    user, exists := auth.GetUserFromGinContext(c)
    if !exists {
        c.JSON(401, gin.H{"error": "用户未认证"})
        return
    }
    
    c.JSON(200, user.ToResponse())
}

// 在 Fiber 处理器中
func getProfileHandler(c *fiber.Ctx) error {
    user, exists := auth.GetUserFromFiberContext(c)
    if !exists {
        return c.Status(401).JSON(fiber.Map{"error": "用户未认证"})
    }
    
    return c.JSON(user.ToResponse())
}
```

## 🔧 配置选项

```go
type Config struct {
    JWTSecret           string // JWT 签名密钥
    TokenExpireHours    int    // JWT 令牌过期时间（小时）
    SessionExpireHours  int    // 会话过期时间（小时）
    PasswordMinLength   int    // 密码最小长度
    RequireSpecialChar  bool   // 是否要求特殊字符
    MaxLoginAttempts    int    // 最大登录尝试次数
    LockoutDurationMin  int    // 锁定持续时间（分钟）
}
```

## 📊 数据模型

### User 用户模型

```go
type User struct {
    ID           int       `json:"id"`
    Username     string    `json:"username"`
    Email        string    `json:"email"`
    PasswordHash string    `json:"-"`          // 不在JSON中显示
    Salt         string    `json:"-"`          // 不在JSON中显示
    CreatedAt    time.Time `json:"created_at"`
    UpdatedAt    time.Time `json:"updated_at"`
    IsActive     bool      `json:"is_active"`
    Role         string    `json:"role"`
}
```

### UserSession 会话模型

```go
type UserSession struct {
    ID        int       `json:"id"`
    UserID    int       `json:"user_id"`
    Token     string    `json:"token"`
    ExpiresAt time.Time `json:"expires_at"`
    CreatedAt time.Time `json:"created_at"`
}
```

## 🛡️ 安全特性

1. **密码加密**：使用 bcrypt 进行密码哈希
2. **JWT 安全**：HMAC-SHA256 签名验证
3. **会话管理**：令牌过期和会话清理
4. **输入验证**：邮箱格式和密码强度验证
5. **角色权限**：基于角色的访问控制

## 🔌 API 接口

### 认证服务方法

- `NewService(db, config)` - 创建认证服务
- `Register(req)` - 用户注册
- `Login(req)` - 用户登录
- `Logout(token)` - 用户登出
- `GetCurrentUser(token)` - 获取当前用户
- `UpdateUserInfo(userID, req)` - 更新用户信息
- `ChangePassword(userID, req)` - 修改密码

### 中间件方法

- `GinAuthMiddleware()` - Gin 认证中间件
- `GinRoleMiddleware(roles...)` - Gin 角色中间件
- `FiberAuthMiddleware()` - Fiber 认证中间件
- `FiberRoleMiddleware(roles...)` - Fiber 角色中间件

## 🧪 测试

```go
// 创建测试用的认证服务
func setupTestAuth() *auth.Service {
    db, _ := sql.Open("sqlite", ":memory:")
    config := auth.NewDefaultConfig()
    authService, _ := auth.NewService(db, config)
    authService.InitializeTables()
    return authService
}
```

## 📄 许可证

MIT License
