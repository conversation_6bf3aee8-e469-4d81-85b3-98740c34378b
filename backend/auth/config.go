// config.go - 认证模块配置
// 定义认证相关的配置结构体和默认值

package auth

// Config 认证配置结构体
type Config struct {
	JWTSecret           string `toml:"jwt_secret"`            // JWT 签名密钥
	TokenExpireHours    int    `toml:"token_expire_hours"`    // JWT 令牌过期时间（小时）
	SessionExpireHours  int    `toml:"session_expire_hours"`  // 会话过期时间（小时）
	PasswordMinLength   int    `toml:"password_min_length"`   // 密码最小长度
	RequireSpecialChar  bool   `toml:"require_special_char"`  // 是否要求特殊字符
	MaxLoginAttempts    int    `toml:"max_login_attempts"`    // 最大登录尝试次数
	LockoutDurationMin  int    `toml:"lockout_duration_min"`  // 锁定持续时间（分钟）
}

// NewDefaultConfig 创建默认认证配置
func NewDefaultConfig() *Config {
	return &Config{
		JWTSecret:           "pixiu-default-jwt-secret-change-in-production",
		TokenExpireHours:    24,
		SessionExpireHours:  168, // 7天
		PasswordMinLength:   8,
		RequireSpecialChar:  true,
		MaxLoginAttempts:    5,
		LockoutDurationMin:  15,
	}
}

// Validate 验证配置的有效性
func (c *Config) Validate() error {
	if c.JWTSecret == "" {
		return ErrInvalidJWTSecret
	}
	
	if c.TokenExpireHours <= 0 {
		return ErrInvalidTokenExpire
	}
	
	if c.SessionExpireHours <= 0 {
		return ErrInvalidSessionExpire
	}
	
	if c.PasswordMinLength < 6 {
		return ErrInvalidPasswordLength
	}
	
	return nil
}
