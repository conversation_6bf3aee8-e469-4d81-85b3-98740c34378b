// middleware.go - 认证中间件
// 为不同框架提供统一的认证中间件支持

package auth

import (
	"context"
	"net/http"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/gofiber/fiber/v2"
)

// 上下文键类型
type contextKey string

const userContextKey contextKey = "user"

// SetUserInContext 将用户信息设置到上下文中
func SetUserInContext(ctx context.Context, user *User) context.Context {
	return context.WithValue(ctx, userContextKey, user)
}

// GetUserFromContext 从上下文中获取用户信息
func GetUserFromContext(ctx context.Context) *User {
	user, ok := ctx.Value(userContextKey).(*User)
	if !ok {
		return nil
	}
	return user
}

// extractTokenFromHeader 从请求头中提取令牌
func extractTokenFromHeader(authHeader string) string {
	if authHeader == "" {
		return ""
	}

	parts := strings.SplitN(authHeader, " ", 2)
	if len(parts) != 2 || parts[0] != "Bearer" {
		return ""
	}

	return parts[1]
}

// GinAuthMiddleware Gin 框架认证中间件
func (s *Service) GinAuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		token := extractTokenFromHeader(c.GetHeader("Authorization"))
		if token == "" {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "缺少认证令牌"})
			c.Abort()
			return
		}

		user, err := s.GetCurrentUser(token)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "无效的认证令牌"})
			c.Abort()
			return
		}

		// 将用户信息存储到上下文中
		c.Set("user", user)
		c.Set("user_id", user.ID)
		c.Set("username", user.Username)
		c.Set("role", user.Role)

		c.Next()
	}
}

// GinRoleMiddleware Gin 框架角色权限中间件
func (s *Service) GinRoleMiddleware(requiredRoles ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		user, exists := c.Get("user")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未认证"})
			c.Abort()
			return
		}

		userObj := user.(*User)
		for _, role := range requiredRoles {
			if userObj.Role == role {
				c.Next()
				return
			}
		}

		c.JSON(http.StatusForbidden, gin.H{"error": "权限不足"})
		c.Abort()
	}
}

// FiberAuthMiddleware Fiber 框架认证中间件
func (s *Service) FiberAuthMiddleware() fiber.Handler {
	return func(c *fiber.Ctx) error {
		token := extractTokenFromHeader(c.Get("Authorization"))
		if token == "" {
			return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
				"error": "缺少认证令牌",
			})
		}

		user, err := s.GetCurrentUser(token)
		if err != nil {
			return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
				"error": "无效的认证令牌",
			})
		}

		// 将用户信息存储到上下文中
		c.Locals("user", user)
		c.Locals("user_id", user.ID)
		c.Locals("username", user.Username)
		c.Locals("role", user.Role)

		return c.Next()
	}
}

// FiberRoleMiddleware Fiber 框架角色权限中间件
func (s *Service) FiberRoleMiddleware(requiredRoles ...string) fiber.Handler {
	return func(c *fiber.Ctx) error {
		user := c.Locals("user")
		if user == nil {
			return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
				"error": "用户未认证",
			})
		}

		userObj := user.(*User)
		for _, role := range requiredRoles {
			if userObj.Role == role {
				return c.Next()
			}
		}

		return c.Status(fiber.StatusForbidden).JSON(fiber.Map{
			"error": "权限不足",
		})
	}
}

// GetUserFromGinContext 从 Gin 上下文中获取用户
func GetUserFromGinContext(c *gin.Context) (*User, bool) {
	user, exists := c.Get("user")
	if !exists {
		return nil, false
	}
	return user.(*User), true
}

// GetUserFromFiberContext 从 Fiber 上下文中获取用户
func GetUserFromFiberContext(c *fiber.Ctx) (*User, bool) {
	user := c.Locals("user")
	if user == nil {
		return nil, false
	}
	return user.(*User), true
}

// GetUserIDFromString 从字符串转换用户ID
func GetUserIDFromString(idStr string) (int, error) {
	id, err := strconv.Atoi(idStr)
	if err != nil {
		return 0, ErrInvalidCredentials
	}
	return id, nil
}

// NetHTTPAuthMiddleware 标准库 net/http 认证中间件
func (s *Service) NetHTTPAuthMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		token := extractTokenFromHeader(r.Header.Get("Authorization"))
		if token == "" {
			http.Error(w, "缺少认证令牌", http.StatusUnauthorized)
			return
		}

		user, err := s.GetCurrentUser(token)
		if err != nil {
			http.Error(w, "无效的认证令牌", http.StatusUnauthorized)
			return
		}

		// 将用户信息存储到请求上下文中
		ctx := r.Context()
		ctx = SetUserInContext(ctx, user)
		r = r.WithContext(ctx)

		next.ServeHTTP(w, r)
	})
}

// NetHTTPRoleMiddleware 标准库 net/http 角色权限中间件
func (s *Service) NetHTTPRoleMiddleware(requiredRoles ...string) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			user := GetUserFromContext(r.Context())
			if user == nil {
				http.Error(w, "用户未认证", http.StatusUnauthorized)
				return
			}

			for _, role := range requiredRoles {
				if user.Role == role {
					next.ServeHTTP(w, r)
					return
				}
			}

			http.Error(w, "权限不足", http.StatusForbidden)
		})
	}
}
