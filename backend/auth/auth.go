// auth.go - 认证服务和 JWT 管理
// 负责JWT令牌管理、会话管理和认证核心逻辑

package auth

import (
	"database/sql"
	"errors"
	"fmt"
	"time"

	"github.com/golang-jwt/jwt/v5"
)

// 错误定义
var (
	ErrInvalidJWTSecret     = errors.New("无效的JWT密钥")
	ErrInvalidTokenExpire   = errors.New("无效的令牌过期时间")
	ErrInvalidSessionExpire = errors.New("无效的会话过期时间")
	ErrInvalidPasswordLength = errors.New("无效的密码长度配置")
	ErrInvalidEmail         = errors.New("邮箱格式无效")
	ErrUserExists           = errors.New("用户名或邮箱已存在")
	ErrUserNotFound         = errors.New("用户不存在")
	ErrInvalidPassword      = errors.New("密码不正确")
	ErrInvalidCredentials   = errors.New("用户名或密码错误")
	ErrUserDisabled         = errors.New("用户账户已被禁用")
	ErrInvalidToken         = errors.New("无效的JWT令牌")
	ErrSessionNotFound      = errors.New("会话不存在或已过期")
)

// Service 认证服务结构体
type Service struct {
	db     *sql.DB
	config *Config
}

// JWTClaims JWT 声明结构体
type JWTClaims struct {
	UserID   int    `json:"user_id"`
	Username string `json:"username"`
	Role     string `json:"role"`
	jwt.RegisteredClaims
}

// LoginResponse 登录响应结构体
type LoginResponse struct {
	Token     string        `json:"token"`
	ExpiresAt time.Time     `json:"expires_at"`
	User      *UserResponse `json:"user"`
}

// NewService 创建认证服务实例
func NewService(db *sql.DB, config *Config) (*Service, error) {
	if config == nil {
		config = NewDefaultConfig()
	}
	
	if err := config.Validate(); err != nil {
		return nil, fmt.Errorf("认证配置验证失败: %w", err)
	}
	
	return &Service{
		db:     db,
		config: config,
	}, nil
}

// GetConfig 获取认证配置
func (s *Service) GetConfig() *Config {
	return s.config
}

// GenerateJWT 生成JWT令牌
func (s *Service) GenerateJWT(user *User) (string, time.Time, error) {
	expiresAt := time.Now().Add(time.Duration(s.config.TokenExpireHours) * time.Hour)
	
	claims := &JWTClaims{
		UserID:   user.ID,
		Username: user.Username,
		Role:     user.Role,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expiresAt),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			Issuer:    "pixiu-auth",
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString([]byte(s.config.JWTSecret))
	if err != nil {
		return "", time.Time{}, fmt.Errorf("生成JWT令牌失败: %w", err)
	}

	return tokenString, expiresAt, nil
}

// ValidateJWT 验证JWT令牌
func (s *Service) ValidateJWT(tokenString string) (*JWTClaims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &JWTClaims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("意外的签名方法: %v", token.Header["alg"])
		}
		return []byte(s.config.JWTSecret), nil
	})

	if err != nil {
		return nil, fmt.Errorf("解析JWT令牌失败: %w", err)
	}

	if claims, ok := token.Claims.(*JWTClaims); ok && token.Valid {
		return claims, nil
	}

	return nil, ErrInvalidToken
}

// CreateSession 创建用户会话
func (s *Service) CreateSession(userID int, token string) error {
	expiresAt := time.Now().Add(time.Duration(s.config.SessionExpireHours) * time.Hour)
	
	_, err := s.db.Exec(`
		INSERT INTO user_sessions (user_id, token, expires_at, created_at)
		VALUES (?, ?, ?, ?)`,
		userID, token, expiresAt, time.Now())
	
	if err != nil {
		return fmt.Errorf("创建会话失败: %w", err)
	}

	return nil
}

// ValidateSession 验证会话
func (s *Service) ValidateSession(token string) (*UserSession, error) {
	session := &UserSession{}
	err := s.db.QueryRow(`
		SELECT id, user_id, token, expires_at, created_at
		FROM user_sessions 
		WHERE token = ? AND expires_at > ?`,
		token, time.Now()).Scan(
		&session.ID, &session.UserID, &session.Token,
		&session.ExpiresAt, &session.CreatedAt)
	
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, ErrSessionNotFound
		}
		return nil, fmt.Errorf("验证会话失败: %w", err)
	}

	return session, nil
}

// DeleteSession 删除会话
func (s *Service) DeleteSession(token string) error {
	_, err := s.db.Exec("DELETE FROM user_sessions WHERE token = ?", token)
	if err != nil {
		return fmt.Errorf("删除会话失败: %w", err)
	}
	return nil
}

// CleanExpiredSessions 清理过期会话
func (s *Service) CleanExpiredSessions() error {
	_, err := s.db.Exec("DELETE FROM user_sessions WHERE expires_at < ?", time.Now())
	if err != nil {
		return fmt.Errorf("清理过期会话失败: %w", err)
	}
	return nil
}

// Login 用户登录
func (s *Service) Login(req *LoginRequest) (*LoginResponse, error) {
	// 获取用户
	user, err := GetUserByUsername(s.db, req.Username)
	if err != nil {
		return nil, ErrInvalidCredentials
	}

	// 检查用户是否激活
	if !user.IsActive {
		return nil, ErrUserDisabled
	}

	// 验证密码
	if !CheckPassword(req.Password, user.PasswordHash) {
		return nil, ErrInvalidCredentials
	}

	// 生成JWT令牌
	token, expiresAt, err := s.GenerateJWT(user)
	if err != nil {
		return nil, err
	}

	// 创建会话
	if err := s.CreateSession(user.ID, token); err != nil {
		return nil, err
	}

	return &LoginResponse{
		Token:     token,
		ExpiresAt: expiresAt,
		User:      user.ToResponse(),
	}, nil
}

// Logout 用户登出
func (s *Service) Logout(token string) error {
	return s.DeleteSession(token)
}

// GetCurrentUser 获取当前用户信息
func (s *Service) GetCurrentUser(token string) (*User, error) {
	// 验证JWT令牌
	claims, err := s.ValidateJWT(token)
	if err != nil {
		return nil, err
	}

	// 验证会话
	_, err = s.ValidateSession(token)
	if err != nil {
		return nil, err
	}

	// 获取用户信息
	return GetUserByID(s.db, claims.UserID)
}

// Register 用户注册
func (s *Service) Register(req *CreateUserRequest) (*User, error) {
	return CreateUser(s.db, req, s.config)
}

// UpdateUserInfo 更新用户信息
func (s *Service) UpdateUserInfo(userID int, req *UpdateUserRequest) (*User, error) {
	return UpdateUser(s.db, userID, req)
}

// ChangePassword 修改用户密码
func (s *Service) ChangePassword(userID int, req *ChangePasswordRequest) error {
	return ChangeUserPassword(s.db, userID, req, s.config)
}

// InitializeTables 初始化数据库表
func (s *Service) InitializeTables() error {
	return CreateUserTables(s.db)
}
