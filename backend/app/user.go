// user.go - 用户管理相关功能
// 负责用户数据模型、CRUD操作和数据库迁移

package app

import (
	"database/sql"
	"fmt"
	"regexp"
	"strings"
	"time"

	"golang.org/x/crypto/bcrypt"
)

// User 用户数据模型
type User struct {
	ID           int       `json:"id" db:"id"`
	Username     string    `json:"username" db:"username"`
	Email        string    `json:"email" db:"email"`
	PasswordHash string    `json:"-" db:"password_hash"` // 不在JSON中显示
	Salt         string    `json:"-" db:"salt"`          // 不在JSON中显示
	CreatedAt    time.Time `json:"created_at" db:"created_at"`
	UpdatedAt    time.Time `json:"updated_at" db:"updated_at"`
	IsActive     bool      `json:"is_active" db:"is_active"`
	Role         string    `json:"role" db:"role"`
}

// UserSession 用户会话数据模型
type UserSession struct {
	ID        int       `json:"id" db:"id"`
	UserID    int       `json:"user_id" db:"user_id"`
	Token     string    `json:"token" db:"token"`
	ExpiresAt time.Time `json:"expires_at" db:"expires_at"`
	CreatedAt time.Time `json:"created_at" db:"created_at"`
}

// CreateUserRequest 创建用户请求结构
type CreateUserRequest struct {
	Username string `json:"username" binding:"required,min=3,max=50"`
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required,min=8"`
	Role     string `json:"role,omitempty"`
}

// LoginRequest 登录请求结构
type LoginRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
}

// UpdateUserRequest 更新用户请求结构
type UpdateUserRequest struct {
	Email    string `json:"email,omitempty" binding:"omitempty,email"`
	IsActive *bool  `json:"is_active,omitempty"`
	Role     string `json:"role,omitempty"`
}

// ChangePasswordRequest 修改密码请求结构
type ChangePasswordRequest struct {
	OldPassword string `json:"old_password" binding:"required"`
	NewPassword string `json:"new_password" binding:"required,min=8"`
}

// UserResponse 用户响应结构（不包含敏感信息）
type UserResponse struct {
	ID        int       `json:"id"`
	Username  string    `json:"username"`
	Email     string    `json:"email"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	IsActive  bool      `json:"is_active"`
	Role      string    `json:"role"`
}

// ToResponse 将 User 转换为 UserResponse
func (u *User) ToResponse() *UserResponse {
	return &UserResponse{
		ID:        u.ID,
		Username:  u.Username,
		Email:     u.Email,
		CreatedAt: u.CreatedAt,
		UpdatedAt: u.UpdatedAt,
		IsActive:  u.IsActive,
		Role:      u.Role,
	}
}

// ValidateEmail 验证邮箱格式
func ValidateEmail(email string) bool {
	emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
	return emailRegex.MatchString(email)
}

// ValidatePassword 验证密码强度
func ValidatePassword(password string, config *AuthConfig) error {
	if len(password) < config.PasswordMinLength {
		return fmt.Errorf("密码长度至少需要 %d 位", config.PasswordMinLength)
	}

	if config.RequireSpecialChar {
		hasSpecial := regexp.MustCompile(`[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]`).MatchString(password)
		hasNumber := regexp.MustCompile(`[0-9]`).MatchString(password)
		hasUpper := regexp.MustCompile(`[A-Z]`).MatchString(password)
		hasLower := regexp.MustCompile(`[a-z]`).MatchString(password)

		if !hasSpecial || !hasNumber || !hasUpper || !hasLower {
			return fmt.Errorf("密码必须包含大写字母、小写字母、数字和特殊字符")
		}
	}

	return nil
}

// HashPassword 加密密码
func HashPassword(password string) (string, error) {
	bytes, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	return string(bytes), err
}

// CheckPassword 验证密码
func CheckPassword(password, hash string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(hash), []byte(password))
	return err == nil
}

// CreateUserTables 创建用户相关表
func CreateUserTables(db *sql.DB) error {
	// 创建用户表
	userTableSQL := `
	CREATE TABLE IF NOT EXISTS users (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		username VARCHAR(50) UNIQUE NOT NULL,
		email VARCHAR(100) UNIQUE NOT NULL,
		password_hash VARCHAR(255) NOT NULL,
		salt VARCHAR(255) NOT NULL,
		created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
		updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
		is_active BOOLEAN DEFAULT TRUE,
		role VARCHAR(20) DEFAULT 'user'
	)`

	if _, err := db.Exec(userTableSQL); err != nil {
		return fmt.Errorf("创建用户表失败: %w", err)
	}

	// 创建用户会话表
	sessionTableSQL := `
	CREATE TABLE IF NOT EXISTS user_sessions (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		user_id INTEGER NOT NULL,
		token VARCHAR(255) UNIQUE NOT NULL,
		expires_at DATETIME NOT NULL,
		created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
		FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
	)`

	if _, err := db.Exec(sessionTableSQL); err != nil {
		return fmt.Errorf("创建用户会话表失败: %w", err)
	}

	// 创建索引
	indexes := []string{
		"CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)",
		"CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)",
		"CREATE INDEX IF NOT EXISTS idx_sessions_token ON user_sessions(token)",
		"CREATE INDEX IF NOT EXISTS idx_sessions_user_id ON user_sessions(user_id)",
		"CREATE INDEX IF NOT EXISTS idx_sessions_expires_at ON user_sessions(expires_at)",
	}

	for _, indexSQL := range indexes {
		if _, err := db.Exec(indexSQL); err != nil {
			return fmt.Errorf("创建索引失败: %w", err)
		}
	}

	return nil
}

// CreateUser 创建新用户
func CreateUser(db *sql.DB, req *CreateUserRequest, config *AuthConfig) (*User, error) {
	// 验证输入
	if !ValidateEmail(req.Email) {
		return nil, fmt.Errorf("邮箱格式无效")
	}

	if err := ValidatePassword(req.Password, config); err != nil {
		return nil, err
	}

	// 检查用户名和邮箱是否已存在
	var count int
	err := db.QueryRow("SELECT COUNT(*) FROM users WHERE username = ? OR email = ?", 
		req.Username, req.Email).Scan(&count)
	if err != nil {
		return nil, fmt.Errorf("检查用户是否存在失败: %w", err)
	}
	if count > 0 {
		return nil, fmt.Errorf("用户名或邮箱已存在")
	}

	// 加密密码
	passwordHash, err := HashPassword(req.Password)
	if err != nil {
		return nil, fmt.Errorf("密码加密失败: %w", err)
	}

	// 设置默认角色
	role := req.Role
	if role == "" {
		role = "user"
	}

	// 插入用户
	now := time.Now()
	result, err := db.Exec(`
		INSERT INTO users (username, email, password_hash, salt, created_at, updated_at, is_active, role)
		VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
		req.Username, req.Email, passwordHash, "", now, now, true, role)
	
	if err != nil {
		return nil, fmt.Errorf("创建用户失败: %w", err)
	}

	userID, err := result.LastInsertId()
	if err != nil {
		return nil, fmt.Errorf("获取用户ID失败: %w", err)
	}

	// 返回创建的用户
	return GetUserByID(db, int(userID))
}

// GetUserByID 根据ID获取用户
func GetUserByID(db *sql.DB, userID int) (*User, error) {
	user := &User{}
	err := db.QueryRow(`
		SELECT id, username, email, password_hash, salt, created_at, updated_at, is_active, role
		FROM users WHERE id = ?`, userID).Scan(
		&user.ID, &user.Username, &user.Email, &user.PasswordHash, &user.Salt,
		&user.CreatedAt, &user.UpdatedAt, &user.IsActive, &user.Role)
	
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("用户不存在")
		}
		return nil, fmt.Errorf("查询用户失败: %w", err)
	}

	return user, nil
}

// GetUserByUsername 根据用户名获取用户
func GetUserByUsername(db *sql.DB, username string) (*User, error) {
	user := &User{}
	err := db.QueryRow(`
		SELECT id, username, email, password_hash, salt, created_at, updated_at, is_active, role
		FROM users WHERE username = ?`, username).Scan(
		&user.ID, &user.Username, &user.Email, &user.PasswordHash, &user.Salt,
		&user.CreatedAt, &user.UpdatedAt, &user.IsActive, &user.Role)
	
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("用户不存在")
		}
		return nil, fmt.Errorf("查询用户失败: %w", err)
	}

	return user, nil
}

// UpdateUser 更新用户信息
func UpdateUser(db *sql.DB, userID int, req *UpdateUserRequest) (*User, error) {
	// 构建更新语句
	setParts := []string{}
	args := []interface{}{}

	if req.Email != "" {
		if !ValidateEmail(req.Email) {
			return nil, fmt.Errorf("邮箱格式无效")
		}
		setParts = append(setParts, "email = ?")
		args = append(args, req.Email)
	}

	if req.IsActive != nil {
		setParts = append(setParts, "is_active = ?")
		args = append(args, *req.IsActive)
	}

	if req.Role != "" {
		setParts = append(setParts, "role = ?")
		args = append(args, req.Role)
	}

	if len(setParts) == 0 {
		return GetUserByID(db, userID) // 没有更新内容，直接返回原用户
	}

	setParts = append(setParts, "updated_at = ?")
	args = append(args, time.Now())
	args = append(args, userID)

	updateSQL := fmt.Sprintf("UPDATE users SET %s WHERE id = ?", strings.Join(setParts, ", "))
	
	_, err := db.Exec(updateSQL, args...)
	if err != nil {
		return nil, fmt.Errorf("更新用户失败: %w", err)
	}

	return GetUserByID(db, userID)
}

// ChangeUserPassword 修改用户密码
func ChangeUserPassword(db *sql.DB, userID int, req *ChangePasswordRequest, config *AuthConfig) error {
	// 获取用户当前密码
	user, err := GetUserByID(db, userID)
	if err != nil {
		return err
	}

	// 验证旧密码
	if !CheckPassword(req.OldPassword, user.PasswordHash) {
		return fmt.Errorf("旧密码不正确")
	}

	// 验证新密码
	if err := ValidatePassword(req.NewPassword, config); err != nil {
		return err
	}

	// 加密新密码
	newPasswordHash, err := HashPassword(req.NewPassword)
	if err != nil {
		return fmt.Errorf("新密码加密失败: %w", err)
	}

	// 更新密码
	_, err = db.Exec("UPDATE users SET password_hash = ?, updated_at = ? WHERE id = ?",
		newPasswordHash, time.Now(), userID)
	if err != nil {
		return fmt.Errorf("更新密码失败: %w", err)
	}

	return nil
}
