// logger.go - 日志相关功能
// 负责日志记录器的初始化、配置和管理

package app

import (
	"fmt"
	"io"
	"log/slog"
	"os"
	"path/filepath"
	"strings"
)

// parseLogLevel 将字符串转换为 slog.Level
func parseLogLevel(level string) slog.Level {
	switch strings.ToLower(level) {
	case "debug":
		return slog.LevelDebug
	case "info":
		return slog.LevelInfo
	case "warn", "warning":
		return slog.LevelWarn
	case "error":
		return slog.LevelError
	default:
		return slog.LevelInfo // 默认级别
	}
}

// createLogWriter 根据配置创建日志输出目标
func createLogWriter(filePath string) (io.Writer, error) {
	if filePath == "" {
		return os.Stdout, nil
	}

	// 确保日志文件目录存在
	dir := filepath.Dir(filePath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return nil, fmt.Errorf("创建日志目录失败: %w", err)
	}

	// 创建或打开日志文件
	file, err := os.OpenFile(filePath, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
	if err != nil {
		return nil, fmt.Errorf("创建日志文件失败: %w", err)
	}

	return file, nil
}

// initLogger 根据配置初始化日志记录器
func initLogger(config *Config) (*slog.Logger, error) {
	// 根据配置创建日志输出目标
	logWriter, err := createLogWriter(config.Log.FilePath)
	if err != nil {
		return nil, fmt.Errorf("创建日志输出目标失败: %w", err)
	}

	// 配置日志选项
	opts := &slog.HandlerOptions{
		Level: parseLogLevel(config.Log.Level),
	}

	// 初始化文本格式的日志处理器
	handler := slog.NewTextHandler(logWriter, opts)
	logger := slog.New(handler)

	return logger, nil
}
