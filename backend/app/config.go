// config.go - 配置相关功能
// 负责应用配置的定义、读取和解析

package app

import (
	"fmt"
	"os"

	"github.com/BurntSushi/toml"
)

// LogConfig 日志配置结构体
type LogConfig struct {
	Level    string `toml:"level"`     // 日志级别：debug, info, warn, error
	FilePath string `toml:"file_path"` // 日志文件路径，为空则输出到标准输出
}

// DatabaseConfig 数据库配置结构体
type DatabaseConfig struct {
	Driver          string `toml:"driver"`             // 数据库驱动：mysql, postgres, sqlite3
	Host            string `toml:"host"`               // 数据库主机地址
	Port            int    `toml:"port"`               // 数据库端口
	Database        string `toml:"database"`           // 数据库名称
	Username        string `toml:"username"`           // 用户名
	Password        string `toml:"password"`           // 密码
	SSLMode         string `toml:"ssl_mode"`           // SSL模式（postgres）
	Charset         string `toml:"charset"`            // 字符集（mysql）
	MaxOpenConns    int    `toml:"max_open_conns"`     // 最大打开连接数
	MaxIdleConns    int    `toml:"max_idle_conns"`     // 最大空闲连接数
	ConnMaxLifetime int    `toml:"conn_max_lifetime"`  // 连接最大生存时间（秒）
	ConnMaxIdleTime int    `toml:"conn_max_idle_time"` // 连接最大空闲时间（秒）
}

// ServerConfig HTTP 服务器配置结构体
type ServerConfig struct {
	Host         string `toml:"host"`          // 服务器主机地址
	Port         int    `toml:"port"`          // 服务器端口
	ReadTimeout  int    `toml:"read_timeout"`  // 读取超时（秒）
	WriteTimeout int    `toml:"write_timeout"` // 写入超时（秒）
	Framework    string `toml:"framework"`     // HTTP 框架：gin, fiber, net/http
}

// Config 应用配置结构体
type Config struct {
	Log      LogConfig      `toml:"log"`      // 日志配置
	Database DatabaseConfig `toml:"database"` // 数据库配置
	Server   ServerConfig   `toml:"server"`   // 服务器配置
}

// newDefaultConfig 创建默认配置
func newDefaultConfig() *Config {
	return &Config{
		Log: LogConfig{
			Level:    "info",
			FilePath: "", // 默认输出到标准输出
		},
		Database: DatabaseConfig{
			Driver:          "sqlite", // 默认使用 SQLite (glebarez/sqlite)
			Host:            "localhost",
			Port:            0,          // SQLite 不需要端口
			Database:        "pixiu.db", // 默认数据库文件
			Username:        "",
			Password:        "",
			SSLMode:         "disable",
			Charset:         "utf8mb4",
			MaxOpenConns:    25,  // 默认最大打开连接数
			MaxIdleConns:    5,   // 默认最大空闲连接数
			ConnMaxLifetime: 300, // 5分钟
			ConnMaxIdleTime: 60,  // 1分钟
		},
		Server: ServerConfig{
			Host:         "localhost", // 默认主机地址
			Port:         8080,        // 默认端口
			ReadTimeout:  30,          // 30秒读取超时
			WriteTimeout: 30,          // 30秒写入超时
			Framework:    "gin",       // 默认使用 Gin 框架
		},
	}
}

// loadConfig 读取并解析配置文件
// 如果 configFile 为空，则返回默认配置
// 如果配置文件不存在或解析失败，返回错误
func loadConfig(configFile string) (*Config, error) {
	// 创建默认配置
	config := newDefaultConfig()

	// 如果没有指定配置文件，使用默认配置
	if configFile == "" {
		return config, nil
	}

	// 检查配置文件是否存在
	if _, err := os.Stat(configFile); err != nil {
		return nil, fmt.Errorf("配置文件不存在: %s, 错误: %w", configFile, err)
	}

	// 解析配置文件
	if _, err := toml.DecodeFile(configFile, config); err != nil {
		return nil, fmt.Errorf("解析配置文件失败: %w", err)
	}

	return config, nil
}
