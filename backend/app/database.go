// database.go - 数据库相关功能
// 负责数据库连接的初始化、配置和管理

package app

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	// 导入数据库驱动
	_ "github.com/glebarez/sqlite"
	// 其他驱动可按需添加：
	// _ "github.com/go-sql-driver/mysql"
	// _ "github.com/lib/pq"
)

// buildConnectionString 根据配置构建数据库连接字符串
func buildConnectionString(config *DatabaseConfig) (string, error) {
	switch config.Driver {
	case "mysql":
		// MySQL 连接字符串格式: username:password@tcp(host:port)/database?charset=utf8mb4&parseTime=True&loc=Local
		dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=%s&parseTime=True&loc=Local",
			config.Username, config.Password, config.Host, config.Port, config.Database, config.Charset)
		return dsn, nil

	case "postgres", "postgresql":
		// PostgreSQL 连接字符串格式: host=localhost port=5432 user=username password=password dbname=database sslmode=disable
		dsn := fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=%s",
			config.Host, config.Port, config.Username, config.Password, config.Database, config.SSLMode)
		return dsn, nil

	case "sqlite3", "sqlite":
		// SQLite 连接字符串格式: database.db 或 file:database.db?options
		// glebarez/sqlite 支持简单的文件路径格式
		dsn := config.Database
		if dsn == "" {
			dsn = "pixiu.db" // 默认数据库文件名
		}
		return dsn, nil

	default:
		return "", fmt.Errorf("不支持的数据库驱动: %s", config.Driver)
	}
}

// initDatabase 初始化数据库连接
func initDatabase(config *Config) (*sql.DB, error) {
	dbConfig := &config.Database

	// 构建连接字符串
	dsn, err := buildConnectionString(dbConfig)
	if err != nil {
		return nil, fmt.Errorf("构建数据库连接字符串失败: %w", err)
	}

	// 打开数据库连接
	db, err := sql.Open(dbConfig.Driver, dsn)
	if err != nil {
		return nil, fmt.Errorf("打开数据库连接失败: %w", err)
	}

	// 配置连接池
	if dbConfig.MaxOpenConns > 0 {
		db.SetMaxOpenConns(dbConfig.MaxOpenConns)
	}
	if dbConfig.MaxIdleConns > 0 {
		db.SetMaxIdleConns(dbConfig.MaxIdleConns)
	}
	if dbConfig.ConnMaxLifetime > 0 {
		db.SetConnMaxLifetime(time.Duration(dbConfig.ConnMaxLifetime) * time.Second)
	}
	if dbConfig.ConnMaxIdleTime > 0 {
		db.SetConnMaxIdleTime(time.Duration(dbConfig.ConnMaxIdleTime) * time.Second)
	}

	// 测试数据库连接
	if err := testDatabaseConnection(db); err != nil {
		db.Close()
		return nil, fmt.Errorf("数据库连接测试失败: %w", err)
	}

	return db, nil
}

// testDatabaseConnection 测试数据库连接
func testDatabaseConnection(db *sql.DB) error {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := db.PingContext(ctx); err != nil {
		return fmt.Errorf("数据库连接测试失败: %w", err)
	}

	return nil
}

// closeDatabase 关闭数据库连接
func closeDatabase(db *sql.DB) error {
	if db != nil {
		return db.Close()
	}
	return nil
}
