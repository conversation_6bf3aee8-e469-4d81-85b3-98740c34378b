# Pixiu 应用配置模块

这个模块提供了基于 TOML 配置文件的应用初始化和日志管理功能。

## 模块结构

该模块已重构为多个独立文件，提高了代码的可维护性和模块化程度：

### 📁 文件组织

- **`app.go`** - 应用核心逻辑
  - `Applicant` 结构体定义
  - `New()` 应用初始化函数
  - 公共访问接口：`GetApp()`, `GetLogger()`, `GetConfig()`
  - 全局应用实例管理

- **`config.go`** - 配置相关功能
  - `Config` 和 `LogConfig` 结构体定义
  - `loadConfig()` 配置文件读取和解析
  - `newDefaultConfig()` 默认配置创建

- **`logger.go`** - 日志相关功能
  - `parseLogLevel()` 日志级别解析
  - `createLogWriter()` 日志输出目标创建
  - `initLogger()` 日志记录器初始化

## 功能特性

- ✅ **TOML 配置文件支持**：使用标准的 TOML 格式配置文件
- ✅ **灵活的日志配置**：支持多种日志级别和输出目标
- ✅ **错误处理**：完善的配置文件和日志文件错误处理
- ✅ **默认配置**：即使没有配置文件也能正常工作
- ✅ **运行时访问**：提供便捷的配置和日志访问接口

## 使用方法

### 1. 基本使用

```go
import "pixiu/backend/app"

// 使用默认配置初始化
err := app.New("")
if err != nil {
    log.Fatal(err)
}

// 使用配置文件初始化
err := app.New("configs/app.toml")
if err != nil {
    log.Fatal(err)
}
```

### 2. 获取日志记录器

```go
logger := app.GetLogger()
logger.Debug("调试信息")
logger.Info("普通信息")
logger.Warn("警告信息")
logger.Error("错误信息")
```

### 3. 获取配置

```go
config := app.GetConfig()
if config != nil {
    fmt.Printf("日志级别: %s\n", config.Log.Level)
    fmt.Printf("日志文件: %s\n", config.Log.FilePath)
}
```

## 配置文件格式

### 基本配置示例 (app.toml)

```toml
[log]
# 日志级别：debug, info, warn, error
level = "info"

# 日志文件路径，留空则输出到标准输出
file_path = "logs/app.log"
```

### 调试配置示例 (debug.toml)

```toml
[log]
level = "debug"
file_path = "logs/debug.log"
```

### 生产环境配置示例 (production.toml)

```toml
[log]
level = "warn"
file_path = "logs/production.log"
```

## 日志级别说明

| 级别    | 描述     | 包含的日志               |
| ------- | -------- | ------------------------ |
| `debug` | 调试级别 | Debug, Info, Warn, Error |
| `info`  | 信息级别 | Info, Warn, Error        |
| `warn`  | 警告级别 | Warn, Error              |
| `error` | 错误级别 | Error                    |

## 错误处理

- **配置文件不存在**：返回详细的错误信息
- **配置文件格式错误**：返回 TOML 解析错误
- **日志文件创建失败**：返回文件系统错误
- **日志目录不存在**：自动创建目录

## 测试示例

查看 `cmd/` 目录下的测试文件：

- `test_config.go` - 基本配置测试
- `test_debug.go` - 调试级别测试
- `test_error.go` - 错误级别测试

运行测试：

```bash
go run cmd/test_config.go
go run cmd/test_debug.go
go run cmd/test_error.go
```
