# Pixiu 应用配置模块

这个模块提供了基于 TOML 配置文件的应用初始化、日志管理、数据库连接和 HTTP 服务器功能。

## 模块结构

该模块已重构为多个独立文件，提高了代码的可维护性和模块化程度：

### 📁 文件组织

- **`app.go`** - 应用核心逻辑
  - `Applicant` 结构体定义（包含日志、数据库、HTTP 服务器）
  - `New()` 应用初始化函数
  - 公共访问接口：`GetApp()`, `GetLogger()`, `GetConfig()`, `GetDB()`, `GetServer()`
  - 资源清理：`Close()` 函数

- **`config.go`** - 配置相关功能
  - `Config`、`LogConfig`、`DatabaseConfig`、`ServerConfig` 结构体定义
  - `loadConfig()` 配置文件读取和解析
  - `newDefaultConfig()` 默认配置创建

- **`logger.go`** - 日志相关功能
  - `parseLogLevel()` 日志级别解析
  - `createLogWriter()` 日志输出目标创建
  - `initLogger()` 日志记录器初始化

- **`database.go`** - 数据库相关功能
  - `buildConnectionString()` 数据库连接字符串构建
  - `initDatabase()` 数据库连接初始化
  - `testDatabaseConnection()` 数据库连接测试
  - 支持 SQLite（无 CGO 依赖）、MySQL、PostgreSQL

- **`server.go`** - HTTP 服务器相关功能
  - `ServerInterface` 服务器接口定义
  - `GinServer`、`FiberServer` 和 `NetHTTPServer` 实现
  - `initServer()` 服务器初始化
  - 支持 Gin 框架、Fiber 框架和标准库 net/http

## 功能特性

- ✅ **TOML 配置文件支持**：使用标准的 TOML 格式配置文件
- ✅ **灵活的日志配置**：支持多种日志级别和输出目标
- ✅ **数据库连接管理**：支持 SQLite（无 CGO）、MySQL、PostgreSQL
- ✅ **HTTP 服务器支持**：支持 Gin 框架和标准库 net/http
- ✅ **连接池管理**：数据库连接池配置和管理
- ✅ **优雅关闭**：支持服务器和数据库连接的优雅关闭
- ✅ **错误处理**：完善的错误处理和日志记录
- ✅ **默认配置**：即使没有配置文件也能正常工作
- ✅ **运行时访问**：提供便捷的配置、日志、数据库、服务器访问接口

## 使用方法

### 1. 基本使用

```go
import "pixiu/backend/app"

// 使用默认配置初始化（包含日志、数据库、HTTP 服务器）
err := app.New("")
if err != nil {
    log.Fatal(err)
}

// 使用配置文件初始化
err := app.New("configs/app.toml")
if err != nil {
    log.Fatal(err)
}

// 确保资源清理
defer app.Close()
```

### 2. 获取日志记录器

```go
logger := app.GetLogger()
logger.Debug("调试信息")
logger.Info("普通信息")
logger.Warn("警告信息")
logger.Error("错误信息")
```

### 3. 获取数据库连接

```go
db := app.GetDB()
if db != nil {
    rows, err := db.Query("SELECT * FROM users")
    // 处理查询结果...
}
```

### 4. 获取 HTTP 服务器

```go
server := app.GetServer()
if server != nil {
    // 获取 Gin 引擎（如果使用 Gin 框架）
    engine := server.GetEngine().(*gin.Engine)

    // 设置路由
    engine.GET("/api/users", func(c *gin.Context) {
        c.JSON(200, gin.H{"message": "Hello World"})
    })

    // 启动服务器
    go server.Start()
}
```

### 5. 获取配置

```go
config := app.GetConfig()
if config != nil {
    fmt.Printf("日志级别: %s\n", config.Log.Level)
    fmt.Printf("数据库驱动: %s\n", config.Database.Driver)
    fmt.Printf("服务器端口: %d\n", config.Server.Port)
}
```

## 配置文件格式

### 完整配置示例 (configs/app.toml)

```toml
[log]
# 日志级别：debug, info, warn, error
level = "info"
# 日志文件路径，留空则输出到标准输出
file_path = "logs/app.log"

[database]
# 数据库驱动：sqlite, mysql, postgres
driver = "sqlite"
# 数据库连接参数
host = "localhost"
port = 0                    # SQLite 不需要端口
database = "pixiu.db"       # SQLite 数据库文件路径
username = ""               # SQLite 不需要用户名
password = ""               # SQLite 不需要密码
# SSL 和字符集配置
ssl_mode = "disable"
charset = "utf8mb4"
# 连接池配置
max_open_conns = 25         # 最大打开连接数
max_idle_conns = 5          # 最大空闲连接数
conn_max_lifetime = 300     # 连接最大生存时间（秒）
conn_max_idle_time = 60     # 连接最大空闲时间（秒）

[server]
# HTTP 服务器配置
host = "localhost"
port = 8080
read_timeout = 30           # 读取超时（秒）
write_timeout = 30          # 写入超时（秒）
framework = "gin"           # HTTP 框架：gin, net/http
```

### MySQL 配置示例

```toml
[database]
driver = "mysql"
host = "localhost"
port = 3306
database = "pixiu_db"
username = "pixiu_user"
password = "your_password"
charset = "utf8mb4"
max_open_conns = 50
max_idle_conns = 10
```

### PostgreSQL 配置示例

```toml
[database]
driver = "postgres"
host = "localhost"
port = 5432
database = "pixiu_db"
username = "pixiu_user"
password = "your_password"
ssl_mode = "disable"
max_open_conns = 50
max_idle_conns = 10
```

## 日志级别说明

| 级别    | 描述     | 包含的日志               |
| ------- | -------- | ------------------------ |
| `debug` | 调试级别 | Debug, Info, Warn, Error |
| `info`  | 信息级别 | Info, Warn, Error        |
| `warn`  | 警告级别 | Warn, Error              |
| `error` | 错误级别 | Error                    |

## 错误处理

- **配置文件不存在**：返回详细的错误信息
- **配置文件格式错误**：返回 TOML 解析错误
- **日志文件创建失败**：返回文件系统错误
- **日志目录不存在**：自动创建目录

## 示例程序

查看 `examples/` 目录下的示例程序：

### 基本配置示例
```bash
go run examples/basic-config/main.go
```
演示不同配置文件场景的使用。

### 数据库连接示例
```bash
go run examples/database-connection/main.go
```
演示数据库连接、表创建、数据插入和查询。

### HTTP 服务器示例
```bash
go run examples/http-server/main.go
```
演示 HTTP 服务器启动、路由设置和 API 处理。

### 调试和错误级别示例
```bash
go run examples/debug-logging/main.go
go run examples/error-level/main.go
```
演示不同日志级别的过滤效果。

## 依赖说明

### 核心依赖
- `github.com/BurntSushi/toml` - TOML 配置文件解析
- `github.com/glebarez/sqlite` - SQLite 数据库驱动（无 CGO 依赖）
- `github.com/gin-gonic/gin` - Gin HTTP Web 框架
- `github.com/gofiber/fiber/v2` - Fiber HTTP Web 框架（高性能）

### 可选依赖
- `github.com/go-sql-driver/mysql` - MySQL 驱动（需要时安装）
- `github.com/lib/pq` - PostgreSQL 驱动（需要时安装）
