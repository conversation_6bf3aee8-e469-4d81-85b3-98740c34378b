// auth.go - 认证相关功能
// 负责JWT令牌管理、会话管理和认证中间件

package app

import (
	"database/sql"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gofiber/fiber/v2"
	"github.com/golang-jwt/jwt/v5"
)

// AuthService 认证服务结构体
type AuthService struct {
	db     *sql.DB
	config *AuthConfig
	logger interface{} // 可以是 *slog.Logger
}

// JWTClaims JWT 声明结构体
type JWTClaims struct {
	UserID   int    `json:"user_id"`
	Username string `json:"username"`
	Role     string `json:"role"`
	jwt.RegisteredClaims
}

// LoginResponse 登录响应结构体
type LoginResponse struct {
	Token     string        `json:"token"`
	ExpiresAt time.Time     `json:"expires_at"`
	User      *UserResponse `json:"user"`
}

// NewAuthService 创建认证服务实例
func NewAuthService(db *sql.DB, config *AuthConfig) *AuthService {
	return &AuthService{
		db:     db,
		config: config,
	}
}

// GenerateJWT 生成JWT令牌
func (a *AuthService) GenerateJWT(user *User) (string, time.Time, error) {
	expiresAt := time.Now().Add(time.Duration(a.config.TokenExpireHours) * time.Hour)

	claims := &JWTClaims{
		UserID:   user.ID,
		Username: user.Username,
		Role:     user.Role,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expiresAt),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			Issuer:    "pixiu-auth",
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString([]byte(a.config.JWTSecret))
	if err != nil {
		return "", time.Time{}, fmt.Errorf("生成JWT令牌失败: %w", err)
	}

	return tokenString, expiresAt, nil
}

// ValidateJWT 验证JWT令牌
func (a *AuthService) ValidateJWT(tokenString string) (*JWTClaims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &JWTClaims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("意外的签名方法: %v", token.Header["alg"])
		}
		return []byte(a.config.JWTSecret), nil
	})

	if err != nil {
		return nil, fmt.Errorf("解析JWT令牌失败: %w", err)
	}

	if claims, ok := token.Claims.(*JWTClaims); ok && token.Valid {
		return claims, nil
	}

	return nil, fmt.Errorf("无效的JWT令牌")
}

// CreateSession 创建用户会话
func (a *AuthService) CreateSession(userID int, token string) error {
	expiresAt := time.Now().Add(time.Duration(a.config.SessionExpireHours) * time.Hour)

	_, err := a.db.Exec(`
		INSERT INTO user_sessions (user_id, token, expires_at, created_at)
		VALUES (?, ?, ?, ?)`,
		userID, token, expiresAt, time.Now())

	if err != nil {
		return fmt.Errorf("创建会话失败: %w", err)
	}

	return nil
}

// ValidateSession 验证会话
func (a *AuthService) ValidateSession(token string) (*UserSession, error) {
	session := &UserSession{}
	err := a.db.QueryRow(`
		SELECT id, user_id, token, expires_at, created_at
		FROM user_sessions 
		WHERE token = ? AND expires_at > ?`,
		token, time.Now()).Scan(
		&session.ID, &session.UserID, &session.Token,
		&session.ExpiresAt, &session.CreatedAt)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("会话不存在或已过期")
		}
		return nil, fmt.Errorf("验证会话失败: %w", err)
	}

	return session, nil
}

// DeleteSession 删除会话
func (a *AuthService) DeleteSession(token string) error {
	_, err := a.db.Exec("DELETE FROM user_sessions WHERE token = ?", token)
	if err != nil {
		return fmt.Errorf("删除会话失败: %w", err)
	}
	return nil
}

// CleanExpiredSessions 清理过期会话
func (a *AuthService) CleanExpiredSessions() error {
	_, err := a.db.Exec("DELETE FROM user_sessions WHERE expires_at < ?", time.Now())
	if err != nil {
		return fmt.Errorf("清理过期会话失败: %w", err)
	}
	return nil
}

// Login 用户登录
func (a *AuthService) Login(req *LoginRequest) (*LoginResponse, error) {
	// 获取用户
	user, err := GetUserByUsername(a.db, req.Username)
	if err != nil {
		return nil, fmt.Errorf("用户名或密码错误")
	}

	// 检查用户是否激活
	if !user.IsActive {
		return nil, fmt.Errorf("用户账户已被禁用")
	}

	// 验证密码
	if !CheckPassword(req.Password, user.PasswordHash) {
		return nil, fmt.Errorf("用户名或密码错误")
	}

	// 生成JWT令牌
	token, expiresAt, err := a.GenerateJWT(user)
	if err != nil {
		return nil, err
	}

	// 创建会话
	if err := a.CreateSession(user.ID, token); err != nil {
		return nil, err
	}

	return &LoginResponse{
		Token:     token,
		ExpiresAt: expiresAt,
		User:      user.ToResponse(),
	}, nil
}

// Logout 用户登出
func (a *AuthService) Logout(token string) error {
	return a.DeleteSession(token)
}

// GetCurrentUser 获取当前用户信息
func (a *AuthService) GetCurrentUser(token string) (*User, error) {
	// 验证JWT令牌
	claims, err := a.ValidateJWT(token)
	if err != nil {
		return nil, err
	}

	// 验证会话
	_, err = a.ValidateSession(token)
	if err != nil {
		return nil, err
	}

	// 获取用户信息
	return GetUserByID(a.db, claims.UserID)
}

// extractTokenFromHeader 从请求头中提取令牌
func extractTokenFromHeader(authHeader string) string {
	if authHeader == "" {
		return ""
	}

	parts := strings.SplitN(authHeader, " ", 2)
	if len(parts) != 2 || parts[0] != "Bearer" {
		return ""
	}

	return parts[1]
}

// GinAuthMiddleware Gin 框架认证中间件
func (a *AuthService) GinAuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		token := extractTokenFromHeader(c.GetHeader("Authorization"))
		if token == "" {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "缺少认证令牌"})
			c.Abort()
			return
		}

		user, err := a.GetCurrentUser(token)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "无效的认证令牌"})
			c.Abort()
			return
		}

		// 将用户信息存储到上下文中
		c.Set("user", user)
		c.Set("user_id", user.ID)
		c.Set("username", user.Username)
		c.Set("role", user.Role)

		c.Next()
	}
}

// GinRoleMiddleware Gin 框架角色权限中间件
func (a *AuthService) GinRoleMiddleware(requiredRoles ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		user, exists := c.Get("user")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未认证"})
			c.Abort()
			return
		}

		userObj := user.(*User)
		for _, role := range requiredRoles {
			if userObj.Role == role {
				c.Next()
				return
			}
		}

		c.JSON(http.StatusForbidden, gin.H{"error": "权限不足"})
		c.Abort()
	}
}

// FiberAuthMiddleware Fiber 框架认证中间件
func (a *AuthService) FiberAuthMiddleware() fiber.Handler {
	return func(c *fiber.Ctx) error {
		token := extractTokenFromHeader(c.Get("Authorization"))
		if token == "" {
			return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
				"error": "缺少认证令牌",
			})
		}

		user, err := a.GetCurrentUser(token)
		if err != nil {
			return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
				"error": "无效的认证令牌",
			})
		}

		// 将用户信息存储到上下文中
		c.Locals("user", user)
		c.Locals("user_id", user.ID)
		c.Locals("username", user.Username)
		c.Locals("role", user.Role)

		return c.Next()
	}
}

// FiberRoleMiddleware Fiber 框架角色权限中间件
func (a *AuthService) FiberRoleMiddleware(requiredRoles ...string) fiber.Handler {
	return func(c *fiber.Ctx) error {
		user := c.Locals("user")
		if user == nil {
			return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
				"error": "用户未认证",
			})
		}

		userObj := user.(*User)
		for _, role := range requiredRoles {
			if userObj.Role == role {
				return c.Next()
			}
		}

		return c.Status(fiber.StatusForbidden).JSON(fiber.Map{
			"error": "权限不足",
		})
	}
}

// GetUserFromGinContext 从 Gin 上下文中获取用户
func GetUserFromGinContext(c *gin.Context) (*User, bool) {
	user, exists := c.Get("user")
	if !exists {
		return nil, false
	}
	return user.(*User), true
}

// GetUserFromFiberContext 从 Fiber 上下文中获取用户
func GetUserFromFiberContext(c *fiber.Ctx) (*User, bool) {
	user := c.Locals("user")
	if user == nil {
		return nil, false
	}
	return user.(*User), true
}

// GetUserIDFromString 从字符串转换用户ID
func GetUserIDFromString(idStr string) (int, error) {
	id, err := strconv.Atoi(idStr)
	if err != nil {
		return 0, fmt.Errorf("无效的用户ID")
	}
	return id, nil
}
