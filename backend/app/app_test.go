package app

import (
	"testing"
)

func TestNewDefaultConfig(t *testing.T) {
	config := newDefaultConfig()
	
	if config == nil {
		t.Fatal("配置不能为空")
	}
	
	if config.Log.Level != "info" {
		t.Errorf("期望日志级别为 'info'，实际为 '%s'", config.Log.Level)
	}
	
	if config.Database.Driver != "sqlite" {
		t.<PERSON><PERSON>("期望数据库驱动为 'sqlite'，实际为 '%s'", config.Database.Driver)
	}
	
	if config.Server.Framework != "gin" {
		t.<PERSON>rrorf("期望服务器框架为 'gin'，实际为 '%s'", config.Server.Framework)
	}
	
	if config.Auth.JWTSecret == "" {
		t.<PERSON>r("JWT 密钥不能为空")
	}
}

func TestValidateEmail(t *testing.T) {
	tests := []struct {
		email string
		valid bool
	}{
		{"<EMAIL>", true},
		{"<EMAIL>", true},
		{"invalid-email", false},
		{"@domain.com", false},
		{"user@", false},
		{"", false},
	}
	
	for _, test := range tests {
		result := ValidateEmail(test.email)
		if result != test.valid {
			t.Errorf("邮箱 '%s' 验证结果期望 %v，实际 %v", test.email, test.valid, result)
		}
	}
}

func TestHashPassword(t *testing.T) {
	password := "TestPassword123!"
	
	hash, err := HashPassword(password)
	if err != nil {
		t.Fatalf("密码加密失败: %v", err)
	}
	
	if hash == "" {
		t.Error("密码哈希不能为空")
	}
	
	if hash == password {
		t.Error("密码哈希不能与原密码相同")
	}
	
	// 验证密码
	if !CheckPassword(password, hash) {
		t.Error("密码验证失败")
	}
	
	// 验证错误密码
	if CheckPassword("WrongPassword", hash) {
		t.Error("错误密码不应该验证通过")
	}
}
