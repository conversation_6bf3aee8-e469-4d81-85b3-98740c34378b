// server.go - HTTP 服务器相关功能
// 负责 HTTP 服务器的初始化、配置和管理

package app

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/logger"
	"github.com/gofiber/fiber/v2/middleware/recover"
)

// ServerInterface 服务器接口，支持不同的 HTTP 框架
type ServerInterface interface {
	Start() error
	Stop(ctx context.Context) error
	GetEngine() interface{}
}

// GinServer Gin 框架服务器实现
type GinServer struct {
	engine *gin.Engine
	server *http.Server
	config *ServerConfig
}

// NewGinServer 创建 Gin 服务器实例
func NewGinServer(config *ServerConfig) *GinServer {
	// 设置 Gin 模式
	gin.SetMode(gin.ReleaseMode)

	engine := gin.New()

	// 添加中间件
	engine.Use(gin.Logger())
	engine.Use(gin.Recovery())

	// 创建 HTTP 服务器
	server := &http.Server{
		Addr:         fmt.Sprintf("%s:%d", config.Host, config.Port),
		Handler:      engine,
		ReadTimeout:  time.Duration(config.ReadTimeout) * time.Second,
		WriteTimeout: time.Duration(config.WriteTimeout) * time.Second,
	}

	return &GinServer{
		engine: engine,
		server: server,
		config: config,
	}
}

// Start 启动服务器
func (s *GinServer) Start() error {
	return s.server.ListenAndServe()
}

// Stop 停止服务器
func (s *GinServer) Stop(ctx context.Context) error {
	return s.server.Shutdown(ctx)
}

// GetEngine 获取 Gin 引擎
func (s *GinServer) GetEngine() interface{} {
	return s.engine
}

// NetHTTPServer 标准库 HTTP 服务器实现
type NetHTTPServer struct {
	mux    *http.ServeMux
	server *http.Server
	config *ServerConfig
}

// NewNetHTTPServer 创建标准库 HTTP 服务器实例
func NewNetHTTPServer(config *ServerConfig) *NetHTTPServer {
	mux := http.NewServeMux()

	server := &http.Server{
		Addr:         fmt.Sprintf("%s:%d", config.Host, config.Port),
		Handler:      mux,
		ReadTimeout:  time.Duration(config.ReadTimeout) * time.Second,
		WriteTimeout: time.Duration(config.WriteTimeout) * time.Second,
	}

	return &NetHTTPServer{
		mux:    mux,
		server: server,
		config: config,
	}
}

// Start 启动服务器
func (s *NetHTTPServer) Start() error {
	return s.server.ListenAndServe()
}

// Stop 停止服务器
func (s *NetHTTPServer) Stop(ctx context.Context) error {
	return s.server.Shutdown(ctx)
}

// GetEngine 获取 HTTP 多路复用器
func (s *NetHTTPServer) GetEngine() interface{} {
	return s.mux
}

// FiberServer Fiber 框架服务器实现
type FiberServer struct {
	app    *fiber.App
	config *ServerConfig
}

// NewFiberServer 创建 Fiber 服务器实例
func NewFiberServer(config *ServerConfig) *FiberServer {
	// 创建 Fiber 应用配置
	fiberConfig := fiber.Config{
		ReadTimeout:  time.Duration(config.ReadTimeout) * time.Second,
		WriteTimeout: time.Duration(config.WriteTimeout) * time.Second,
		ServerHeader: "Pixiu-Fiber",
		AppName:      "Pixiu v1.0.0",
	}

	// 创建 Fiber 应用
	app := fiber.New(fiberConfig)

	// 添加中间件
	app.Use(logger.New())
	app.Use(recover.New())

	return &FiberServer{
		app:    app,
		config: config,
	}
}

// Start 启动服务器
func (s *FiberServer) Start() error {
	addr := fmt.Sprintf("%s:%d", s.config.Host, s.config.Port)
	return s.app.Listen(addr)
}

// Stop 停止服务器
func (s *FiberServer) Stop(ctx context.Context) error {
	return s.app.ShutdownWithContext(ctx)
}

// GetEngine 获取 Fiber 应用
func (s *FiberServer) GetEngine() interface{} {
	return s.app
}

// initServer 根据配置初始化 HTTP 服务器
func initServer(config *Config) (ServerInterface, error) {
	serverConfig := &config.Server

	switch serverConfig.Framework {
	case "gin":
		return NewGinServer(serverConfig), nil
	case "fiber":
		return NewFiberServer(serverConfig), nil
	case "net/http":
		return NewNetHTTPServer(serverConfig), nil
	default:
		return nil, fmt.Errorf("不支持的 HTTP 框架: %s", serverConfig.Framework)
	}
}
