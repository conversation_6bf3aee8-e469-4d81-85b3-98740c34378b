// app.go - 应用核心逻辑
// 负责应用的初始化、实例管理和公共接口

package app

import (
	"context"
	"database/sql"
	"fmt"
	"log/slog"
	"pixiu/backend/auth"
	"time"
)

type Applicant struct {
	Name   string
	Logger *slog.Logger
	Config *Config
	DB     *sql.DB         // 数据库连接
	Server ServerInterface // HTTP 服务器
	Auth   *auth.Service   // 认证服务
}

var app *Applicant

func New(configfile string) (err error) {
	app = &Applicant{}

	// 加载配置
	config, err := loadConfig(configfile)
	if err != nil {
		return fmt.Errorf("加载配置失败: %w", err)
	}

	// 保存配置到应用实例
	app.Config = config

	// 初始化日志记录器
	logger, err := initLogger(config)
	if err != nil {
		return fmt.Errorf("初始化日志记录器失败: %w", err)
	}
	app.Logger = logger

	// 初始化数据库连接
	db, err := initDatabase(config)
	if err != nil {
		return fmt.Errorf("初始化数据库连接失败: %w", err)
	}
	app.DB = db

	// 初始化 HTTP 服务器
	server, err := initServer(config)
	if err != nil {
		return fmt.Errorf("初始化 HTTP 服务器失败: %w", err)
	}
	app.Server = server

	// 初始化认证服务
	authService, err := auth.NewService(db, &config.Auth)
	if err != nil {
		return fmt.Errorf("初始化认证服务失败: %w", err)
	}
	app.Auth = authService

	// 创建用户表（如果不存在）
	if err := authService.InitializeTables(); err != nil {
		app.Logger.Warn("创建用户表失败", "error", err)
	}

	// 记录初始化成功信息
	app.Logger.Info("应用初始化成功",
		"config_file", configfile,
		"log_level", config.Log.Level,
		"log_output", func() string {
			if config.Log.FilePath == "" {
				return "标准输出"
			}
			return config.Log.FilePath
		}(),
		"database_driver", config.Database.Driver,
		"database_name", config.Database.Database,
		"server_framework", config.Server.Framework,
		"server_address", fmt.Sprintf("%s:%d", config.Server.Host, config.Server.Port))

	return nil
}

// GetApp 获取应用实例
func GetApp() *Applicant {
	return app
}

// GetLogger 获取日志记录器
func GetLogger() *slog.Logger {
	if app != nil {
		return app.Logger
	}
	return nil
}

// GetConfig 获取配置
func GetConfig() *Config {
	if app != nil {
		return app.Config
	}
	return nil
}

// GetDB 获取数据库连接
func GetDB() *sql.DB {
	if app != nil {
		return app.DB
	}
	return nil
}

// GetServer 获取 HTTP 服务器
func GetServer() ServerInterface {
	if app != nil {
		return app.Server
	}
	return nil
}

// GetAuth 获取认证服务
func GetAuth() *auth.Service {
	if app != nil {
		return app.Auth
	}
	return nil
}

// Close 关闭应用资源
func Close() error {
	if app != nil {
		// 关闭 HTTP 服务器
		if app.Server != nil {
			ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
			defer cancel()

			if err := app.Server.Stop(ctx); err != nil {
				if app.Logger != nil {
					app.Logger.Error("关闭 HTTP 服务器失败", "error", err)
				}
				return fmt.Errorf("关闭 HTTP 服务器失败: %w", err)
			}
		}

		// 关闭数据库连接
		if app.DB != nil {
			if err := closeDatabase(app.DB); err != nil {
				if app.Logger != nil {
					app.Logger.Error("关闭数据库连接失败", "error", err)
				}
				return fmt.Errorf("关闭数据库连接失败: %w", err)
			}
		}

		if app.Logger != nil {
			app.Logger.Info("应用资源已清理")
		}
	}
	return nil
}
