// 认证服务器示例程序
// 演示完整的用户认证功能

package main

import (
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"strconv"
	"syscall"

	"github.com/gin-gonic/gin"
	"pixiu/backend/app"
)

func main() {
	fmt.Println("=== Pixiu 认证服务器示例 ===")
	fmt.Println("本示例演示完整的用户认证功能\n")

	// 初始化应用
	err := app.New("configs/app.toml")
	if err != nil {
		fmt.Printf("应用初始化失败: %v\n", err)
		return
	}

	// 确保在程序结束时清理资源
	defer func() {
		if err := app.Close(); err != nil {
			fmt.Printf("清理资源失败: %v\n", err)
		}
	}()

	// 获取服务器实例
	server := app.GetServer()
	if server == nil {
		fmt.Println("获取 HTTP 服务器失败")
		return
	}

	// 获取 Gin 引擎并设置路由
	engine := server.GetEngine().(*gin.Engine)
	setupAuthRoutes(engine)

	logger := app.GetLogger()
	config := app.GetConfig()

	fmt.Printf("认证服务器配置:\n")
	fmt.Printf("  框架: %s\n", config.Server.Framework)
	fmt.Printf("  地址: %s:%d\n", config.Server.Host, config.Server.Port)
	fmt.Printf("  JWT过期时间: %d 小时\n", config.Auth.TokenExpireHours)
	fmt.Printf("  密码最小长度: %d 位\n", config.Auth.PasswordMinLength)

	// 启动服务器（在 goroutine 中）
	go func() {
		logger.Info("启动认证服务器",
			"address", fmt.Sprintf("%s:%d", config.Server.Host, config.Server.Port))

		if err := server.Start(); err != nil && err != http.ErrServerClosed {
			logger.Error("认证服务器启动失败", "error", err)
		}
	}()

	fmt.Printf("\n🚀 认证服务器已启动！\n")
	fmt.Printf("API 端点:\n")
	fmt.Printf("  POST /api/v1/auth/register - 用户注册\n")
	fmt.Printf("  POST /api/v1/auth/login - 用户登录\n")
	fmt.Printf("  POST /api/v1/auth/logout - 用户登出\n")
	fmt.Printf("  GET  /api/v1/auth/profile - 获取用户信息\n")
	fmt.Printf("  PUT  /api/v1/auth/profile - 更新用户信息\n")
	fmt.Printf("  PUT  /api/v1/auth/password - 修改密码\n")
	fmt.Printf("  GET  /api/v1/admin/users - 管理员：获取用户列表\n")
	fmt.Printf("\n访问 http://%s:%d 查看服务状态\n", config.Server.Host, config.Server.Port)
	fmt.Printf("按 Ctrl+C 停止服务器\n\n")

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	fmt.Println("\n正在关闭服务器...")
	logger.Info("收到停止信号，正在关闭服务器")
}

// setupAuthRoutes 设置认证相关路由
func setupAuthRoutes(engine *gin.Engine) {
	auth := app.GetAuth()

	// 公开路由
	engine.GET("/", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"message": "Pixiu 认证服务器",
			"version": "1.0.0",
			"status":  "running",
		})
	})

	// 健康检查
	engine.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status": "healthy",
		})
	})

	// API 路由组
	api := engine.Group("/api/v1")

	// 认证路由组
	authGroup := api.Group("/auth")
	{
		authGroup.POST("/register", registerHandler)
		authGroup.POST("/login", loginHandler)
		authGroup.POST("/logout", auth.GinAuthMiddleware(), logoutHandler)
		authGroup.GET("/profile", auth.GinAuthMiddleware(), getProfileHandler)
		authGroup.PUT("/profile", auth.GinAuthMiddleware(), updateProfileHandler)
		authGroup.PUT("/password", auth.GinAuthMiddleware(), changePasswordHandler)
	}

	// 管理员路由组
	adminGroup := api.Group("/admin")
	adminGroup.Use(auth.GinAuthMiddleware())
	adminGroup.Use(auth.GinRoleMiddleware("admin"))
	{
		adminGroup.GET("/users", getUsersHandler)
		adminGroup.PUT("/users/:id", updateUserHandler)
	}
}

// registerHandler 用户注册
func registerHandler(c *gin.Context) {
	var req app.CreateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "请求参数无效"})
		return
	}

	db := app.GetDB()
	config := app.GetConfig()

	user, err := app.CreateUser(db, &req, &config.Auth)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message": "用户注册成功",
		"user":    user.ToResponse(),
	})
}

// loginHandler 用户登录
func loginHandler(c *gin.Context) {
	var req app.LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "请求参数无效"})
		return
	}

	auth := app.GetAuth()
	response, err := auth.Login(&req)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "登录成功",
		"data":    response,
	})
}

// logoutHandler 用户登出
func logoutHandler(c *gin.Context) {
	token := c.GetHeader("Authorization")
	if token != "" && len(token) > 7 {
		token = token[7:] // 移除 "Bearer " 前缀
	}

	auth := app.GetAuth()
	if err := auth.Logout(token); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "登出失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "登出成功"})
}

// getProfileHandler 获取用户信息
func getProfileHandler(c *gin.Context) {
	user, exists := app.GetUserFromGinContext(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未认证"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"user": user.ToResponse(),
	})
}

// updateProfileHandler 更新用户信息
func updateProfileHandler(c *gin.Context) {
	user, exists := app.GetUserFromGinContext(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未认证"})
		return
	}

	var req app.UpdateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "请求参数无效"})
		return
	}

	db := app.GetDB()
	updatedUser, err := app.UpdateUser(db, user.ID, &req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "用户信息更新成功",
		"user":    updatedUser.ToResponse(),
	})
}

// changePasswordHandler 修改密码
func changePasswordHandler(c *gin.Context) {
	user, exists := app.GetUserFromGinContext(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未认证"})
		return
	}

	var req app.ChangePasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "请求参数无效"})
		return
	}

	db := app.GetDB()
	config := app.GetConfig()

	if err := app.ChangeUserPassword(db, user.ID, &req, &config.Auth); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "密码修改成功"})
}

// getUsersHandler 获取用户列表（管理员）
func getUsersHandler(c *gin.Context) {
	db := app.GetDB()

	rows, err := db.Query(`
		SELECT id, username, email, created_at, updated_at, is_active, role
		FROM users ORDER BY created_at DESC`)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "查询用户失败"})
		return
	}
	defer rows.Close()

	var users []app.UserResponse
	for rows.Next() {
		var user app.UserResponse
		if err := rows.Scan(&user.ID, &user.Username, &user.Email,
			&user.CreatedAt, &user.UpdatedAt, &user.IsActive, &user.Role); err != nil {
			continue
		}
		users = append(users, user)
	}

	c.JSON(http.StatusOK, gin.H{
		"users": users,
		"count": len(users),
	})
}

// updateUserHandler 更新用户（管理员）
func updateUserHandler(c *gin.Context) {
	userID, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的用户ID"})
		return
	}

	var req app.UpdateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "请求参数无效"})
		return
	}

	db := app.GetDB()
	updatedUser, err := app.UpdateUser(db, userID, &req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "用户更新成功",
		"user":    updatedUser.ToResponse(),
	})
}
