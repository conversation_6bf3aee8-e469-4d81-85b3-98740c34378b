#!/bin/bash

# Pixiu 认证 API 测试脚本

BASE_URL="http://localhost:8080/api/v1"
TOKEN=""

echo "=== Pixiu 认证 API 测试 ==="
echo

# 测试用户注册
echo "1. 测试用户注册..."
REGISTER_RESPONSE=$(curl -s -X POST "$BASE_URL/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "email": "<EMAIL>",
    "password": "Test123!@#",
    "role": "user"
  }')

echo "注册响应: $REGISTER_RESPONSE"
echo

# 测试管理员注册
echo "2. 测试管理员注册..."
ADMIN_REGISTER_RESPONSE=$(curl -s -X POST "$BASE_URL/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "email": "<EMAIL>",
    "password": "Admin123!@#",
    "role": "admin"
  }')

echo "管理员注册响应: $ADMIN_REGISTER_RESPONSE"
echo

# 测试用户登录
echo "3. 测试用户登录..."
LOGIN_RESPONSE=$(curl -s -X POST "$BASE_URL/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "password": "Test123!@#"
  }')

echo "登录响应: $LOGIN_RESPONSE"

# 提取 token
TOKEN=$(echo $LOGIN_RESPONSE | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
echo "提取的 Token: $TOKEN"
echo

if [ -z "$TOKEN" ]; then
  echo "登录失败，无法获取 token，停止测试"
  exit 1
fi

# 测试获取用户信息
echo "4. 测试获取用户信息..."
PROFILE_RESPONSE=$(curl -s -X GET "$BASE_URL/auth/profile" \
  -H "Authorization: Bearer $TOKEN")

echo "用户信息响应: $PROFILE_RESPONSE"
echo

# 测试更新用户信息
echo "5. 测试更新用户信息..."
UPDATE_RESPONSE=$(curl -s -X PUT "$BASE_URL/auth/profile" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>"
  }')

echo "更新用户信息响应: $UPDATE_RESPONSE"
echo

# 测试修改密码
echo "6. 测试修改密码..."
PASSWORD_RESPONSE=$(curl -s -X PUT "$BASE_URL/auth/password" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "old_password": "Test123!@#",
    "new_password": "NewTest123!@#"
  }')

echo "修改密码响应: $PASSWORD_RESPONSE"
echo

# 测试管理员登录
echo "7. 测试管理员登录..."
ADMIN_LOGIN_RESPONSE=$(curl -s -X POST "$BASE_URL/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "Admin123!@#"
  }')

echo "管理员登录响应: $ADMIN_LOGIN_RESPONSE"

# 提取管理员 token
ADMIN_TOKEN=$(echo $ADMIN_LOGIN_RESPONSE | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
echo "管理员 Token: $ADMIN_TOKEN"
echo

if [ -n "$ADMIN_TOKEN" ]; then
  # 测试获取用户列表（管理员权限）
  echo "8. 测试获取用户列表（管理员权限）..."
  USERS_RESPONSE=$(curl -s -X GET "$BASE_URL/admin/users" \
    -H "Authorization: Bearer $ADMIN_TOKEN")

  echo "用户列表响应: $USERS_RESPONSE"
  echo
fi

# 测试用户登出
echo "9. 测试用户登出..."
LOGOUT_RESPONSE=$(curl -s -X POST "$BASE_URL/auth/logout" \
  -H "Authorization: Bearer $TOKEN")

echo "登出响应: $LOGOUT_RESPONSE"
echo

# 测试登出后访问受保护资源
echo "10. 测试登出后访问受保护资源..."
PROTECTED_RESPONSE=$(curl -s -X GET "$BASE_URL/auth/profile" \
  -H "Authorization: Bearer $TOKEN")

echo "受保护资源响应: $PROTECTED_RESPONSE"
echo

echo "=== API 测试完成 ==="
