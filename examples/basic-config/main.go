// 基本配置示例程序
// 演示如何使用不同的配置文件场景

package main

import (
	"fmt"

	"pixiu/backend/app"
)

func main() {
	fmt.Println("=== Pixiu 应用配置示例 ===")
	fmt.Println("本示例演示如何使用不同的配置文件场景\n")
	
	// 测试不同的配置文件场景
	testCases := []struct {
		name       string
		configFile string
	}{
		{"默认配置（无配置文件）", ""},
		{"使用示例配置文件", "configs/app.toml"},
		{"不存在的配置文件", "configs/nonexistent.toml"},
	}

	for _, tc := range testCases {
		fmt.Printf("\n=== 测试场景: %s ===\n", tc.name)
		
		err := app.New(tc.configFile)
		if err != nil {
			fmt.Printf("错误: %v\n", err)
			continue
		}

		// 获取日志记录器并测试不同级别的日志
		logger := app.GetLogger()
		if logger != nil {
			logger.Debug("这是一条调试信息")
			logger.Info("这是一条信息日志")
			logger.Warn("这是一条警告日志")
			logger.Error("这是一条错误日志")
		}

		// 显示当前配置
		config := app.GetConfig()
		if config != nil {
			fmt.Printf("当前配置 - 日志级别: %s, 输出目标: %s\n", 
				config.Log.Level, 
				func() string {
					if config.Log.FilePath == "" {
						return "标准输出"
					}
					return config.Log.FilePath
				}())
		}
	}
	
	fmt.Println("\n示例运行完成！")
}
