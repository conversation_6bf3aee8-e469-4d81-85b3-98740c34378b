// 错误级别日志示例程序
// 演示如何使用错误级别日志过滤功能

package main

import (
	"fmt"

	"pixiu/backend/app"
)

func main() {
	fmt.Println("=== Pixiu 错误级别日志示例 ===")
	fmt.Println("本示例演示错误级别日志过滤功能\n")
	
	err := app.New("configs/error.toml")
	if err != nil {
		fmt.Printf("错误: %v\n", err)
		return
	}

	logger := app.GetLogger()
	if logger != nil {
		fmt.Println("发送不同级别的日志（只有错误级别应该显示）:")
		logger.Debug("这是一条调试信息 - 不应该显示")
		logger.Info("这是一条信息日志 - 不应该显示")
		logger.Warn("这是一条警告日志 - 不应该显示") 
		logger.Error("这是一条错误日志 - 应该显示")
	}

	config := app.GetConfig()
	if config != nil {
		fmt.Printf("\n当前配置:\n")
		fmt.Printf("  日志级别: %s\n", config.Log.Level)
		fmt.Printf("  输出目标: %s\n", func() string {
			if config.Log.FilePath == "" {
				return "标准输出"
			}
			return config.Log.FilePath
		}())
	}
	
	fmt.Println("\n注意：由于日志级别设置为 'error'，只有错误级别的日志会显示")
	fmt.Println("示例运行完成！")
}
