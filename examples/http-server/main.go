// HTTP 服务器示例程序
// 演示如何使用 HTTP 服务器功能

package main

import (
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"

	"pixiu/backend/app"

	"github.com/gin-gonic/gin"
)

func main() {
	fmt.Println("=== Pixiu HTTP 服务器示例 ===")
	fmt.Println("本示例演示 HTTP 服务器启动和路由处理")

	// 初始化应用（包含 HTTP 服务器）
	err := app.New("configs/app.toml")
	if err != nil {
		fmt.Printf("应用初始化失败: %v\n", err)
		return
	}

	// 确保在程序结束时清理资源
	defer func() {
		if err := app.Close(); err != nil {
			fmt.Printf("清理资源失败: %v\n", err)
		}
	}()

	// 获取服务器实例
	server := app.GetServer()
	if server == nil {
		fmt.Println("获取 HTTP 服务器失败")
		return
	}

	// 获取 Gin 引擎并设置路由
	engine := server.GetEngine().(*gin.Engine)
	setupRoutes(engine)

	logger := app.GetLogger()
	config := app.GetConfig()

	fmt.Printf("HTTP 服务器配置:\n")
	fmt.Printf("  框架: %s\n", config.Server.Framework)
	fmt.Printf("  地址: %s:%d\n", config.Server.Host, config.Server.Port)
	fmt.Printf("  读取超时: %d 秒\n", config.Server.ReadTimeout)
	fmt.Printf("  写入超时: %d 秒\n", config.Server.WriteTimeout)

	// 启动服务器（在 goroutine 中）
	go func() {
		logger.Info("启动 HTTP 服务器",
			"address", fmt.Sprintf("%s:%d", config.Server.Host, config.Server.Port))

		if err := server.Start(); err != nil && err != http.ErrServerClosed {
			logger.Error("HTTP 服务器启动失败", "error", err)
		}
	}()

	fmt.Printf("\n🚀 HTTP 服务器已启动！\n")
	fmt.Printf("访问 http://%s:%d 查看示例页面\n", config.Server.Host, config.Server.Port)
	fmt.Printf("按 Ctrl+C 停止服务器\n\n")

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	fmt.Println("\n正在关闭服务器...")
	logger.Info("收到停止信号，正在关闭服务器")
}

// setupRoutes 设置路由
func setupRoutes(engine *gin.Engine) {
	// 首页
	engine.GET("/", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"message": "欢迎使用 Pixiu HTTP 服务器！",
			"version": "1.0.0",
			"status":  "running",
		})
	})

	// 健康检查
	engine.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":    "healthy",
			"timestamp": "2025-05-29T16:00:00Z",
		})
	})

	// API 路由组
	api := engine.Group("/api/v1")
	{
		// 用户相关路由
		users := api.Group("/users")
		{
			users.GET("", getUserList)
			users.GET("/:id", getUserByID)
			users.POST("", createUser)
		}

		// 数据库状态
		api.GET("/database/status", getDatabaseStatus)
	}

	// 静态文件（如果需要）
	engine.Static("/static", "./static")
}

// getUserList 获取用户列表
func getUserList(c *gin.Context) {
	db := app.GetDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "数据库连接不可用",
		})
		return
	}

	// 查询用户数据
	rows, err := db.Query("SELECT id, name, email FROM users ORDER BY id")
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "查询用户失败",
		})
		return
	}
	defer rows.Close()

	var users []map[string]interface{}
	for rows.Next() {
		var id int
		var name, email string
		if err := rows.Scan(&id, &name, &email); err != nil {
			continue
		}
		users = append(users, map[string]interface{}{
			"id":    id,
			"name":  name,
			"email": email,
		})
	}

	c.JSON(http.StatusOK, gin.H{
		"users": users,
		"count": len(users),
	})
}

// getUserByID 根据 ID 获取用户
func getUserByID(c *gin.Context) {
	id := c.Param("id")

	db := app.GetDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "数据库连接不可用",
		})
		return
	}

	var userID int
	var name, email string
	err := db.QueryRow("SELECT id, name, email FROM users WHERE id = ?", id).Scan(&userID, &name, &email)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "用户不存在",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"user": map[string]interface{}{
			"id":    userID,
			"name":  name,
			"email": email,
		},
	})
}

// createUser 创建用户
func createUser(c *gin.Context) {
	var req struct {
		Name  string `json:"name" binding:"required"`
		Email string `json:"email" binding:"required,email"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "请求参数无效",
		})
		return
	}

	db := app.GetDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "数据库连接不可用",
		})
		return
	}

	result, err := db.Exec("INSERT INTO users (name, email) VALUES (?, ?)", req.Name, req.Email)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "创建用户失败",
		})
		return
	}

	id, _ := result.LastInsertId()
	c.JSON(http.StatusCreated, gin.H{
		"message": "用户创建成功",
		"user": map[string]interface{}{
			"id":    id,
			"name":  req.Name,
			"email": req.Email,
		},
	})
}

// getDatabaseStatus 获取数据库状态
func getDatabaseStatus(c *gin.Context) {
	db := app.GetDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"status": "disconnected",
			"error":  "数据库连接不可用",
		})
		return
	}

	if err := db.Ping(); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"status": "error",
			"error":  err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"status": "connected",
		"driver": app.GetConfig().Database.Driver,
	})
}
