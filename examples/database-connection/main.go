// 数据库连接示例程序
// 演示如何使用数据库连接功能

package main

import (
	"database/sql"
	"fmt"

	"pixiu/backend/app"
)

func main() {
	fmt.Println("=== Pixiu 数据库连接示例 ===")
	fmt.Println("本示例演示数据库连接和基本操作\n")

	// 初始化应用（包含数据库连接）
	err := app.New("configs/app.toml")
	if err != nil {
		fmt.Printf("应用初始化失败: %v\n", err)
		return
	}

	// 确保在程序结束时清理资源
	defer func() {
		if err := app.Close(); err != nil {
			fmt.Printf("清理资源失败: %v\n", err)
		}
	}()

	// 获取数据库连接
	db := app.GetDB()
	if db == nil {
		fmt.Println("获取数据库连接失败")
		return
	}

	logger := app.GetLogger()
	config := app.GetConfig()

	fmt.Printf("数据库连接成功！\n")
	fmt.Printf("  驱动: %s\n", config.Database.Driver)
	fmt.Printf("  数据库: %s\n", config.Database.Database)
	fmt.Printf("  最大连接数: %d\n", config.Database.MaxOpenConns)
	fmt.Printf("  最大空闲连接数: %d\n", config.Database.MaxIdleConns)

	// 创建示例表
	if err := createExampleTable(db); err != nil {
		logger.Error("创建示例表失败", "error", err)
		return
	}
	logger.Info("示例表创建成功")

	// 插入示例数据
	if err := insertExampleData(db); err != nil {
		logger.Error("插入示例数据失败", "error", err)
		return
	}
	logger.Info("示例数据插入成功")

	// 查询示例数据
	if err := queryExampleData(db); err != nil {
		logger.Error("查询示例数据失败", "error", err)
		return
	}

	fmt.Println("\n数据库操作示例完成！")
}

// createExampleTable 创建示例表
func createExampleTable(db *sql.DB) error {
	query := `
	CREATE TABLE IF NOT EXISTS users (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		name TEXT NOT NULL,
		email TEXT UNIQUE NOT NULL,
		created_at DATETIME DEFAULT CURRENT_TIMESTAMP
	)`

	_, err := db.Exec(query)
	return err
}

// insertExampleData 插入示例数据
func insertExampleData(db *sql.DB) error {
	query := `INSERT OR IGNORE INTO users (name, email) VALUES (?, ?)`

	users := []struct {
		name  string
		email string
	}{
		{"张三", "<EMAIL>"},
		{"李四", "<EMAIL>"},
		{"王五", "<EMAIL>"},
	}

	for _, user := range users {
		_, err := db.Exec(query, user.name, user.email)
		if err != nil {
			return err
		}
	}

	return nil
}

// queryExampleData 查询示例数据
func queryExampleData(db *sql.DB) error {
	query := `SELECT id, name, email, created_at FROM users ORDER BY id`

	rows, err := db.Query(query)
	if err != nil {
		return err
	}
	defer rows.Close()

	fmt.Println("\n用户列表:")
	fmt.Println("ID\t姓名\t邮箱\t\t\t创建时间")
	fmt.Println("--\t----\t----\t\t\t--------")

	for rows.Next() {
		var id int
		var name, email, createdAt string

		if err := rows.Scan(&id, &name, &email, &createdAt); err != nil {
			return err
		}

		fmt.Printf("%d\t%s\t%s\t\t%s\n", id, name, email, createdAt)
	}

	return rows.Err()
}
