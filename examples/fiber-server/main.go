// Fiber HTTP 服务器示例程序
// 演示如何使用 Fiber 框架功能

package main

import (
	"fmt"
	"os"
	"os/signal"
	"syscall"

	"pixiu/backend/app"

	"github.com/gofiber/fiber/v2"
)

func main() {
	fmt.Println("=== Pixiu Fiber 服务器示例 ===")
	fmt.Println("本示例演示 Fiber 框架服务器启动和路由处理\n")

	// 初始化应用（使用 Fiber 框架）
	err := app.New("configs/fiber.toml")
	if err != nil {
		fmt.Printf("应用初始化失败: %v\n", err)
		return
	}

	// 确保在程序结束时清理资源
	defer func() {
		if err := app.Close(); err != nil {
			fmt.Printf("清理资源失败: %v\n", err)
		}
	}()

	// 获取服务器实例
	server := app.GetServer()
	if server == nil {
		fmt.Println("获取 HTTP 服务器失败")
		return
	}

	// 获取 Fiber 应用并设置路由
	fiberApp := server.GetEngine().(*fiber.App)
	setupRoutes(fiberApp)

	logger := app.GetLogger()
	config := app.GetConfig()

	fmt.Printf("Fiber 服务器配置:\n")
	fmt.Printf("  框架: %s\n", config.Server.Framework)
	fmt.Printf("  地址: %s:%d\n", config.Server.Host, config.Server.Port)
	fmt.Printf("  读取超时: %d 秒\n", config.Server.ReadTimeout)
	fmt.Printf("  写入超时: %d 秒\n", config.Server.WriteTimeout)

	// 启动服务器（在 goroutine 中）
	go func() {
		logger.Info("启动 Fiber 服务器",
			"address", fmt.Sprintf("%s:%d", config.Server.Host, config.Server.Port))

		if err := server.Start(); err != nil {
			logger.Error("Fiber 服务器启动失败", "error", err)
		}
	}()

	fmt.Printf("\n🚀 Fiber 服务器已启动！\n")
	fmt.Printf("访问 http://%s:%d 查看示例页面\n", config.Server.Host, config.Server.Port)
	fmt.Printf("按 Ctrl+C 停止服务器\n\n")

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	fmt.Println("\n正在关闭服务器...")
	logger.Info("收到停止信号，正在关闭服务器")
}

// setupRoutes 设置路由
func setupRoutes(app *fiber.App) {
	// 首页
	app.Get("/", func(c *fiber.Ctx) error {
		return c.JSON(fiber.Map{
			"message":   "欢迎使用 Pixiu Fiber 服务器！",
			"version":   "1.0.0",
			"status":    "running",
			"framework": "fiber",
		})
	})

	// 健康检查
	app.Get("/health", func(c *fiber.Ctx) error {
		return c.JSON(fiber.Map{
			"status":    "healthy",
			"timestamp": "2025-05-29T16:00:00Z",
			"framework": "fiber",
		})
	})

	// API 路由组
	api := app.Group("/api/v1")

	// 用户相关路由
	users := api.Group("/users")
	users.Get("/", getUserList)
	users.Get("/:id", getUserByID)
	users.Post("/", createUser)

	// 数据库状态
	api.Get("/database/status", getDatabaseStatus)

	// 静态文件（如果需要）
	app.Static("/static", "./static")
}

// getUserList 获取用户列表
func getUserList(c *fiber.Ctx) error {
	db := app.GetDB()
	if db == nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "数据库连接不可用",
		})
	}

	// 查询用户数据
	rows, err := db.Query("SELECT id, name, email FROM users ORDER BY id")
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "查询用户失败",
		})
	}
	defer rows.Close()

	var users []fiber.Map
	for rows.Next() {
		var id int
		var name, email string
		if err := rows.Scan(&id, &name, &email); err != nil {
			continue
		}
		users = append(users, fiber.Map{
			"id":    id,
			"name":  name,
			"email": email,
		})
	}

	return c.JSON(fiber.Map{
		"users": users,
		"count": len(users),
	})
}

// getUserByID 根据 ID 获取用户
func getUserByID(c *fiber.Ctx) error {
	id := c.Params("id")

	db := app.GetDB()
	if db == nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "数据库连接不可用",
		})
	}

	var userID int
	var name, email string
	err := db.QueryRow("SELECT id, name, email FROM users WHERE id = ?", id).Scan(&userID, &name, &email)
	if err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
			"error": "用户不存在",
		})
	}

	return c.JSON(fiber.Map{
		"user": fiber.Map{
			"id":    userID,
			"name":  name,
			"email": email,
		},
	})
}

// createUser 创建用户
func createUser(c *fiber.Ctx) error {
	var req struct {
		Name  string `json:"name"`
		Email string `json:"email"`
	}

	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "请求参数无效",
		})
	}

	// 简单验证
	if req.Name == "" || req.Email == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "姓名和邮箱不能为空",
		})
	}

	db := app.GetDB()
	if db == nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "数据库连接不可用",
		})
	}

	result, err := db.Exec("INSERT INTO users (name, email) VALUES (?, ?)", req.Name, req.Email)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "创建用户失败",
		})
	}

	id, _ := result.LastInsertId()
	return c.Status(fiber.StatusCreated).JSON(fiber.Map{
		"message": "用户创建成功",
		"user": fiber.Map{
			"id":    id,
			"name":  req.Name,
			"email": req.Email,
		},
	})
}

// getDatabaseStatus 获取数据库状态
func getDatabaseStatus(c *fiber.Ctx) error {
	db := app.GetDB()
	if db == nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"status": "disconnected",
			"error":  "数据库连接不可用",
		})
	}

	if err := db.Ping(); err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"status": "error",
			"error":  err.Error(),
		})
	}

	return c.JSON(fiber.Map{
		"status": "connected",
		"driver": app.GetConfig().Database.Driver,
	})
}
