// 调试日志示例程序
// 演示如何使用调试级别配置和日志文件输出

package main

import (
	"fmt"
	"os"

	"pixiu/backend/app"
)

func main() {
	fmt.Println("=== Pixiu 调试日志示例 ===")
	fmt.Println("本示例演示调试级别日志和文件输出功能\n")
	
	err := app.New("configs/debug.toml")
	if err != nil {
		fmt.Printf("错误: %v\n", err)
		return
	}

	fmt.Println("发送不同级别的日志消息:")
	logger := app.GetLogger()
	if logger != nil {
		logger.Debug("这是一条调试信息 - 应该在 debug 级别显示")
		logger.Info("这是一条信息日志")
		logger.Warn("这是一条警告日志") 
		logger.Error("这是一条错误日志")
	}

	config := app.GetConfig()
	if config != nil {
		fmt.Printf("\n当前配置:\n")
		fmt.Printf("  日志级别: %s\n", config.Log.Level)
		fmt.Printf("  输出目标: %s\n", config.Log.FilePath)
	}
	
	fmt.Println("\n检查日志文件内容:")
	if config != nil && config.Log.FilePath != "" {
		if content, err := os.ReadFile(config.Log.FilePath); err == nil {
			fmt.Printf("日志文件内容:\n%s", string(content))
		} else {
			fmt.Printf("读取日志文件失败: %v\n", err)
		}
	}
	
	fmt.Println("\n示例运行完成！")
}
