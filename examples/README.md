# Pixiu 应用示例程序

本目录包含了 Pixiu 应用配置模块的示例程序，演示了不同的配置和日志功能。

## 📁 示例目录结构

```
examples/
├── README.md                 # 本文件
├── basic-config/            # 基本配置示例
│   └── main.go
├── database-connection/     # 数据库连接示例
│   └── main.go
├── debug-logging/           # 调试日志示例
│   └── main.go
├── error-level/             # 错误级别日志示例
│   └── main.go
├── fiber-server/            # Fiber 框架服务器示例
│   └── main.go
├── framework-test/          # 框架测试示例
│   └── main.go
└── http-server/             # Gin 框架服务器示例
    └── main.go
```

## 🚀 运行示例

### 1. 基本配置示例

演示如何使用不同的配置文件场景：

```bash
cd examples/basic-config
go run main.go
```

**功能展示：**
- 默认配置（无配置文件）
- 使用示例配置文件
- 处理不存在的配置文件

### 2. 调试日志示例

演示调试级别日志和文件输出功能：

```bash
cd examples/debug-logging
go run main.go
```

**功能展示：**
- 调试级别日志输出
- 日志文件写入
- 日志文件内容读取

### 3. 数据库连接示例

演示数据库连接和基本操作：

```bash
cd examples/database-connection
go run main.go
```

**功能展示：**
- SQLite 数据库连接（无 CGO 依赖）
- 数据表创建和数据插入
- 数据查询和结果显示
- 数据库连接池管理

### 4. Gin HTTP 服务器示例

演示 Gin 框架 HTTP 服务器启动和 API 处理：

```bash
cd examples/http-server
go run main.go
```

**功能展示：**
- Gin 框架 HTTP 服务器
- RESTful API 路由设置
- 数据库集成的 API 端点
- 优雅的服务器关闭

### 5. Fiber HTTP 服务器示例

演示 Fiber 框架 HTTP 服务器启动和 API 处理：

```bash
cd examples/fiber-server
go run main.go
```

**功能展示：**
- Fiber 框架 HTTP 服务器（高性能）
- RESTful API 路由设置
- 数据库集成的 API 端点
- 优雅的服务器关闭

**API 端点（Gin 和 Fiber 相同）：**
- `GET /` - 首页信息
- `GET /health` - 健康检查
- `GET /api/v1/users` - 获取用户列表
- `GET /api/v1/users/:id` - 获取指定用户
- `POST /api/v1/users` - 创建新用户
- `GET /api/v1/database/status` - 数据库状态

### 6. 框架测试示例

演示不同 HTTP 框架的初始化测试：

```bash
cd examples/framework-test
go run main.go
```

**功能展示：**
- 测试 Gin 和 Fiber 框架初始化
- 验证配置文件解析
- 检查服务器实例创建

### 7. 错误级别日志示例

演示错误级别日志过滤功能：

```bash
cd examples/error-level
go run main.go
```

**功能展示：**
- 日志级别过滤
- 只显示错误级别的日志
- 其他级别日志被过滤

## 📋 前置条件

运行示例前，请确保：

1. **配置文件存在**：
   - `configs/app.toml` - 基本配置文件
   - `configs/debug.toml` - 调试配置文件
   - `configs/error.toml` - 错误级别配置文件

2. **从项目根目录运行**：
   ```bash
   # 从项目根目录运行示例
   go run examples/basic-config/main.go
   go run examples/debug-logging/main.go
   go run examples/error-level/main.go
   ```

## 🔧 自定义示例

您可以基于这些示例创建自己的配置：

1. 复制任一示例目录
2. 修改配置文件路径
3. 调整日志输出和处理逻辑
4. 运行自定义示例

## 📚 相关文档

- [应用配置模块文档](../backend/app/README.md)
- [配置文件格式说明](../configs/)

## 🐛 故障排除

如果遇到问题：

1. **配置文件不存在**：检查配置文件路径是否正确
2. **权限问题**：确保有日志文件写入权限
3. **编译错误**：确保从正确的目录运行命令
