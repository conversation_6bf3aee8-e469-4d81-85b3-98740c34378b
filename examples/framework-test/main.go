// 框架测试程序
// 测试不同框架的初始化

package main

import (
	"fmt"
	"time"

	"pixiu/backend/app"
)

func main() {
	fmt.Println("=== Pixiu 框架测试 ===")
	fmt.Println("测试不同 HTTP 框架的初始化\n")

	// 测试不同框架
	frameworks := []struct {
		name       string
		configFile string
	}{
		{"Gin 框架", "configs/app.toml"},
		{"Fiber 框架", "configs/fiber.toml"},
	}

	for _, fw := range frameworks {
		fmt.Printf("测试 %s...\n", fw.name)

		err := app.New(fw.configFile)
		if err != nil {
			fmt.Printf("❌ %s 初始化失败: %v\n", fw.name, err)
			continue
		}

		config := app.GetConfig()
		server := app.GetServer()

		if config != nil && server != nil {
			fmt.Printf("✅ %s 初始化成功\n", fw.name)
			fmt.Printf("   框架: %s\n", config.Server.Framework)
			fmt.Printf("   地址: %s:%d\n", config.Server.Host, config.Server.Port)
			fmt.Printf("   引擎类型: %T\n", server.GetEngine())
		} else {
			fmt.Printf("❌ %s 初始化不完整\n", fw.name)
		}

		// 清理资源
		if err := app.Close(); err != nil {
			fmt.Printf("⚠️  %s 资源清理失败: %v\n", fw.name, err)
		}

		fmt.Println()
		time.Sleep(100 * time.Millisecond) // 短暂延迟
	}

	fmt.Println("框架测试完成！")
}
