# Pixiu

一个现代化的 Go Web 应用框架，提供完整的后端开发解决方案。

## 🚀 特性

- ✅ **多框架支持**：支持 Gin、Fiber 和标准库 net/http
- ✅ **数据库集成**：支持 SQLite（无 CGO）、MySQL、PostgreSQL
- ✅ **用户认证**：完整的 JWT 认证和权限管理系统
- ✅ **配置管理**：基于 TOML 的灵活配置系统
- ✅ **日志系统**：结构化日志记录和多级别输出
- ✅ **开发工具**：完整的 Makefile 和开发工具链
- ✅ **容器化**：Docker 支持和多平台编译

## 📦 快速开始

### 安装依赖

```bash
make deps
```

### 编译项目

```bash
# 编译所有程序
make build

# 仅编译后端
make build-backend

# 跨平台编译
make build-all
```

### 运行示例

```bash
# 运行认证服务器
make run

# 或者直接运行
./bin/example-auth-server
```

### 开发模式

```bash
# 热编译开发模式
make watch
```

## 🛠️ 开发工具

### 测试

```bash
# 运行测试
make test

# 生成测试覆盖率报告
make test-coverage
```

### 代码质量

```bash
# 格式化代码
make fmt

# 代码检查
make lint
```

### 数据库

```bash
# 同步数据库表结构
make syncdb
```

### 清理

```bash
# 清理编译产物
make clean
```

## 📁 项目结构

```
pixiu/
├── backend/app/          # 核心应用模块
│   ├── app.go           # 应用核心逻辑
│   ├── config.go        # 配置管理
│   ├── logger.go        # 日志系统
│   ├── database.go      # 数据库管理
│   ├── server.go        # HTTP 服务器
│   ├── auth.go          # 认证系统
│   └── user.go          # 用户管理
├── examples/            # 示例程序
│   ├── auth-server/     # 认证服务器示例
│   ├── http-server/     # HTTP 服务器示例
│   ├── fiber-server/    # Fiber 框架示例
│   └── ...
├── configs/             # 配置文件
│   ├── app.toml         # 主配置文件
│   └── fiber.toml       # Fiber 配置示例
├── bin/                 # 编译输出目录
├── Makefile             # 构建工具
└── Dockerfile           # 容器化支持
```

## 🔧 配置

主配置文件 `configs/app.toml`：

```toml
[log]
level = "info"
file_path = "logs/app.log"

[database]
driver = "sqlite"
database = "pixiu.db"
max_open_conns = 25
max_idle_conns = 5

[server]
host = "localhost"
port = 8080
framework = "gin"

[auth]
jwt_secret = "your-secret-key"
token_expire_hours = 24
password_min_length = 8
```

## 🌐 API 端点

### 认证 API

- `POST /api/v1/auth/register` - 用户注册
- `POST /api/v1/auth/login` - 用户登录
- `POST /api/v1/auth/logout` - 用户登出
- `GET /api/v1/auth/profile` - 获取用户信息
- `PUT /api/v1/auth/profile` - 更新用户信息
- `PUT /api/v1/auth/password` - 修改密码

### 管理员 API

- `GET /api/v1/admin/users` - 获取用户列表
- `PUT /api/v1/admin/users/:id` - 更新用户

## 🐳 Docker 支持

```bash
# 构建 Docker 镜像
make docker-build

# 运行容器
docker run -p 8080:8080 pixiu:latest
```

## 📊 Make 命令

运行 `make help` 查看所有可用命令：

```bash
make help
```

## 🧪 测试 API

使用提供的测试脚本：

```bash
# 启动认证服务器
./bin/example-auth-server &

# 运行 API 测试
./examples/auth-server/test_api.sh
```

## 📚 文档

- [应用模块文档](backend/app/README.md)
- [示例程序文档](examples/README.md)

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License