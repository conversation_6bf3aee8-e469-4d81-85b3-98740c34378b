# Pixiu 项目 Makefile
# 用于自动化编译、打包和开发工具

# ==================== 项目配置 ====================
PROJECT_NAME := pixiu
VERSION := $(shell git describe --tags --always --dirty 2>/dev/null || echo "v0.1.0")
BUILD_TIME := $(shell date -u '+%Y-%m-%d_%H:%M:%S')
GIT_COMMIT := $(shell git rev-parse --short HEAD 2>/dev/null || echo "unknown")
GO_VERSION := $(shell go version | awk '{print $$3}')

# 目录配置
ROOT_DIR := $(shell pwd)
BACKEND_DIR := $(ROOT_DIR)/backend
FRONTEND_DIR := $(ROOT_DIR)/frontend
CMD_DIR := $(ROOT_DIR)/cmd
EXAMPLES_DIR := $(ROOT_DIR)/examples
BIN_DIR := $(ROOT_DIR)/bin
CONFIG_DIR := $(ROOT_DIR)/configs

# Go 编译配置
GOOS := $(shell go env GOOS)
GOARCH := $(shell go env GOARCH)
CGO_ENABLED := 0

# 编译标志
LDFLAGS := -ldflags "-s -w \
	-X 'main.Version=$(VERSION)' \
	-X 'main.BuildTime=$(BUILD_TIME)' \
	-X 'main.GitCommit=$(GIT_COMMIT)' \
	-X 'main.GoVersion=$(GO_VERSION)'"

# 可执行文件扩展名（Windows 支持）
ifeq ($(GOOS),windows)
	EXT := .exe
else
	EXT :=
endif

# 颜色输出
RED := \033[31m
GREEN := \033[32m
YELLOW := \033[33m
BLUE := \033[34m
RESET := \033[0m

# ==================== 默认目标 ====================
.PHONY: all
all: clean deps build ## 执行完整的构建流程

# ==================== 帮助信息 ====================
.PHONY: help
help: ## 显示所有可用的 make 目标和说明
	@echo -e "$(BLUE)Pixiu 项目 Makefile$(RESET)"
	@echo -e "$(BLUE)===========================================$(RESET)"
	@echo ""
	@echo -e "$(GREEN)编译目标:$(RESET)"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  \033[33m%-20s\033[0m %s\n", $$1, $$2}' $(MAKEFILE_LIST) | grep -E "(build|clean)"
	@echo ""
	@echo -e "$(GREEN)开发工具:$(RESET)"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  \033[33m%-20s\033[0m %s\n", $$1, $$2}' $(MAKEFILE_LIST) | grep -E "(watch|run|test)"
	@echo ""
	@echo -e "$(GREEN)数据库管理:$(RESET)"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  \033[33m%-20s\033[0m %s\n", $$1, $$2}' $(MAKEFILE_LIST) | grep -E "(syncdb|migrate)"
	@echo ""
	@echo -e "$(GREEN)其他工具:$(RESET)"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  \033[33m%-20s\033[0m %s\n", $$1, $$2}' $(MAKEFILE_LIST) | grep -v -E "(build|clean|watch|run|test|syncdb|migrate)"
	@echo ""
	@echo -e "$(BLUE)示例:$(RESET)"
	@echo -e "  make build          # 编译所有程序"
	@echo -e "  make watch          # 热编译开发模式"
	@echo -e "  make run            # 运行主程序"
	@echo -e "  make syncdb         # 同步数据库"
	@echo ""

# ==================== 依赖管理 ====================
.PHONY: deps
deps: ## 安装项目依赖
	@echo -e "$(GREEN)安装 Go 依赖...$(RESET)"
	@go mod download
	@go mod tidy
	@echo -e "$(GREEN)检查开发工具...$(RESET)"
	@$(MAKE) check-tools

.PHONY: check-tools
check-tools: ## 检查并安装开发工具
	@echo -e "$(YELLOW)检查 gowatch...$(RESET)"
	@which gowatch > /dev/null 2>&1 || (echo -e "$(YELLOW)安装 gowatch...$(RESET)" && go install github.com/silenceper/gowatch@latest)
	@echo -e "$(YELLOW)检查 golangci-lint...$(RESET)"
	@which golangci-lint > /dev/null 2>&1 || (echo -e "$(YELLOW)请手动安装 golangci-lint: https://golangci-lint.run/usage/install/$(RESET)")

# ==================== 编译目标 ====================
.PHONY: build
build: build-backend ## 编译所有后端程序

.PHONY: build-backend
build-backend: clean-bin ## 仅编译后端 Go 程序
	@echo -e "$(GREEN)编译后端程序...$(RESET)"
	@mkdir -p $(BIN_DIR)
	@if [ -d "$(CMD_DIR)" ]; then \
		for dir in $(CMD_DIR)/*/; do \
			if [ -d "$$dir" ] && [ -f "$$dir/main.go" ]; then \
				app_name=$$(basename "$$dir"); \
				echo -e "$(YELLOW)编译 $$app_name...$(RESET)"; \
				CGO_ENABLED=$(CGO_ENABLED) go build $(LDFLAGS) -o $(BIN_DIR)/$$app_name$(EXT) $$dir; \
			fi; \
		done; \
	fi
	@if [ -d "$(EXAMPLES_DIR)" ]; then \
		echo -e "$(YELLOW)编译示例程序...$(RESET)"; \
		for dir in $(EXAMPLES_DIR)/*/; do \
			if [ -d "$$dir" ] && [ -f "$$dir/main.go" ]; then \
				app_name=$$(basename "$$dir"); \
				echo -e "$(YELLOW)编译示例 $$app_name...$(RESET)"; \
				CGO_ENABLED=$(CGO_ENABLED) go build $(LDFLAGS) -o $(BIN_DIR)/example-$$app_name$(EXT) $$dir; \
			fi; \
		done; \
	fi
	@echo -e "$(GREEN)编译完成！输出目录: $(BIN_DIR)$(RESET)"

.PHONY: build-frontend
build-frontend: ## 仅编译前端程序（如果存在）
	@if [ -d "$(FRONTEND_DIR)" ]; then \
		echo -e "$(GREEN)编译前端程序...$(RESET)"; \
		cd $(FRONTEND_DIR) && npm run build; \
	else \
		echo -e "$(YELLOW)前端目录不存在，跳过前端编译$(RESET)"; \
	fi

.PHONY: build-all
build-all: ## 跨平台编译所有程序
	@echo -e "$(GREEN)跨平台编译...$(RESET)"
	@for os in linux darwin windows; do \
		for arch in amd64 arm64; do \
			if [ "$$os" = "windows" ] && [ "$$arch" = "arm64" ]; then continue; fi; \
			echo -e "$(YELLOW)编译 $$os/$$arch...$(RESET)"; \
			$(MAKE) build-platform GOOS=$$os GOARCH=$$arch; \
		done; \
	done

.PHONY: build-platform
build-platform: ## 为指定平台编译（内部使用）
	@mkdir -p $(BIN_DIR)/$(GOOS)-$(GOARCH)
	@if [ -d "$(CMD_DIR)" ]; then \
		for dir in $(CMD_DIR)/*/; do \
			if [ -d "$$dir" ] && [ -f "$$dir/main.go" ]; then \
				app_name=$$(basename "$$dir"); \
				ext=""; \
				if [ "$(GOOS)" = "windows" ]; then ext=".exe"; fi; \
				CGO_ENABLED=$(CGO_ENABLED) GOOS=$(GOOS) GOARCH=$(GOARCH) go build $(LDFLAGS) \
					-o $(BIN_DIR)/$(GOOS)-$(GOARCH)/$$app_name$$ext $$dir; \
			fi; \
		done; \
	fi

# ==================== 清理目标 ====================
.PHONY: clean
clean: clean-bin clean-cache ## 清理所有编译产物

.PHONY: clean-bin
clean-bin: ## 清理编译产物
	@echo -e "$(YELLOW)清理编译产物...$(RESET)"
	@rm -rf $(BIN_DIR)

.PHONY: clean-cache
clean-cache: ## 清理缓存
	@echo -e "$(YELLOW)清理 Go 缓存...$(RESET)"
	@go clean -cache
	@go clean -modcache

# ==================== 开发工具 ====================
.PHONY: watch
watch: check-tools ## 使用 gowatch 工具热编译后端 Go 代码
	@echo -e "$(GREEN)启动热编译模式...$(RESET)"
	@echo -e "$(YELLOW)监听目录: backend/, cmd/, examples/$(RESET)"
	@gowatch -o $(BIN_DIR)/$(PROJECT_NAME)$(EXT) -p $(BACKEND_DIR) -p $(CMD_DIR) -p $(EXAMPLES_DIR) \
		-i .git -i $(BIN_DIR) -i logs -i tmp \
		--args="$(CONFIG_DIR)/app.toml"

.PHONY: run
run: build ## 运行主程序
	@echo -e "$(GREEN)运行主程序...$(RESET)"
	@if [ -f "$(BIN_DIR)/example-auth-server$(EXT)" ]; then \
		$(BIN_DIR)/example-auth-server$(EXT); \
	elif [ -f "$(BIN_DIR)/example-http-server$(EXT)" ]; then \
		$(BIN_DIR)/example-http-server$(EXT); \
	else \
		echo -e "$(RED)未找到可运行的程序$(RESET)"; \
		exit 1; \
	fi

.PHONY: test
test: ## 运行测试
	@echo -e "$(GREEN)运行测试...$(RESET)"
	@go test -v ./backend/...

.PHONY: test-coverage
test-coverage: ## 运行测试并生成覆盖率报告
	@echo -e "$(GREEN)运行测试覆盖率分析...$(RESET)"
	@go test -v -coverprofile=coverage.out ./backend/...
	@go tool cover -html=coverage.out -o coverage.html
	@echo -e "$(GREEN)覆盖率报告已生成: coverage.html$(RESET)"

.PHONY: lint
lint: check-tools ## 运行代码检查
	@echo -e "$(GREEN)运行代码检查...$(RESET)"
	@golangci-lint run ./...

# ==================== 数据库管理 ====================
.PHONY: syncdb
syncdb: ## 同步数据库表结构
	@echo -e "$(GREEN)同步数据库表结构...$(RESET)"
	@if [ -f "$(BIN_DIR)/example-auth-server$(EXT)" ]; then \
		echo -e "$(YELLOW)使用认证服务器进行数据库同步...$(RESET)"; \
		timeout 5s $(BIN_DIR)/example-auth-server$(EXT) || true; \
	else \
		echo -e "$(YELLOW)编译认证服务器...$(RESET)"; \
		CGO_ENABLED=$(CGO_ENABLED) go build $(LDFLAGS) -o $(BIN_DIR)/example-auth-server$(EXT) $(EXAMPLES_DIR)/auth-server; \
		echo -e "$(YELLOW)运行数据库同步...$(RESET)"; \
		timeout 5s $(BIN_DIR)/example-auth-server$(EXT) || true; \
	fi
	@echo -e "$(GREEN)数据库同步完成$(RESET)"

.PHONY: migrate
migrate: syncdb ## 数据库迁移（别名）

# ==================== 其他实用目标 ====================
.PHONY: info
info: ## 显示项目信息
	@echo -e "$(BLUE)项目信息:$(RESET)"
	@echo "  项目名称: $(PROJECT_NAME)"
	@echo "  版本: $(VERSION)"
	@echo "  构建时间: $(BUILD_TIME)"
	@echo "  Git 提交: $(GIT_COMMIT)"
	@echo "  Go 版本: $(GO_VERSION)"
	@echo "  目标平台: $(GOOS)/$(GOARCH)"
	@echo "  CGO: $(CGO_ENABLED)"

.PHONY: fmt
fmt: ## 格式化代码
	@echo -e "$(GREEN)格式化代码...$(RESET)"
	@go fmt ./...

.PHONY: mod
mod: ## 整理 Go 模块
	@echo -e "$(GREEN)整理 Go 模块...$(RESET)"
	@go mod tidy
	@go mod verify

.PHONY: vendor
vendor: ## 创建 vendor 目录
	@echo -e "$(GREEN)创建 vendor 目录...$(RESET)"
	@go mod vendor

.PHONY: docker-build
docker-build: ## 构建 Docker 镜像
	@echo -e "$(GREEN)构建 Docker 镜像...$(RESET)"
	@docker build -t $(PROJECT_NAME):$(VERSION) .

.PHONY: install
install: build ## 安装程序到 GOPATH/bin
	@echo -e "$(GREEN)安装程序...$(RESET)"
	@if [ -f "$(BIN_DIR)/example-auth-server$(EXT)" ]; then \
		cp $(BIN_DIR)/example-auth-server$(EXT) $(GOPATH)/bin/pixiu-auth$(EXT); \
		echo -e "$(GREEN)已安装 pixiu-auth 到 $(GOPATH)/bin$(RESET)"; \
	fi

# ==================== 特殊目标 ====================
# 确保 bin 目录存在
$(BIN_DIR):
	@mkdir -p $(BIN_DIR)

# 防止文件名冲突
.PHONY: $(shell ls)
