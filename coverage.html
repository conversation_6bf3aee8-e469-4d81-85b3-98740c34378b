
<!DOCTYPE html>
<html>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
		<title>app: Go Coverage Report</title>
		<style>
			body {
				background: black;
				color: rgb(80, 80, 80);
			}
			body, pre, #legend span {
				font-family: Menlo, monospace;
				font-weight: bold;
			}
			#topbar {
				background: black;
				position: fixed;
				top: 0; left: 0; right: 0;
				height: 42px;
				border-bottom: 1px solid rgb(80, 80, 80);
			}
			#content {
				margin-top: 50px;
			}
			#nav, #legend {
				float: left;
				margin-left: 10px;
			}
			#legend {
				margin-top: 12px;
			}
			#nav {
				margin-top: 10px;
			}
			#legend span {
				margin: 0 5px;
			}
			.cov0 { color: rgb(192, 0, 0) }
.cov1 { color: rgb(128, 128, 128) }
.cov2 { color: rgb(116, 140, 131) }
.cov3 { color: rgb(104, 152, 134) }
.cov4 { color: rgb(92, 164, 137) }
.cov5 { color: rgb(80, 176, 140) }
.cov6 { color: rgb(68, 188, 143) }
.cov7 { color: rgb(56, 200, 146) }
.cov8 { color: rgb(44, 212, 149) }
.cov9 { color: rgb(32, 224, 152) }
.cov10 { color: rgb(20, 236, 155) }

		</style>
	</head>
	<body>
		<div id="topbar">
			<div id="nav">
				<select id="files">
				
				<option value="file0">pixiu/backend/app/app.go (0.0%)</option>
				
				<option value="file1">pixiu/backend/app/auth.go (0.0%)</option>
				
				<option value="file2">pixiu/backend/app/config.go (11.1%)</option>
				
				<option value="file3">pixiu/backend/app/database.go (0.0%)</option>
				
				<option value="file4">pixiu/backend/app/logger.go (0.0%)</option>
				
				<option value="file5">pixiu/backend/app/server.go (0.0%)</option>
				
				<option value="file6">pixiu/backend/app/user.go (5.8%)</option>
				
				</select>
			</div>
			<div id="legend">
				<span>not tracked</span>
			
				<span class="cov0">not covered</span>
				<span class="cov8">covered</span>
			
			</div>
		</div>
		<div id="content">
		
		<pre class="file" id="file0" style="display: none">// app.go - 应用核心逻辑
// 负责应用的初始化、实例管理和公共接口

package app

import (
        "context"
        "database/sql"
        "fmt"
        "log/slog"
        "time"
)

type Applicant struct {
        Name   string
        Logger *slog.Logger
        Config *Config
        DB     *sql.DB         // 数据库连接
        Server ServerInterface // HTTP 服务器
        Auth   *AuthService    // 认证服务
}

var app *Applicant

func New(configfile string) (err error) <span class="cov0" title="0">{
        app = &amp;Applicant{}

        // 加载配置
        config, err := loadConfig(configfile)
        if err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("加载配置失败: %w", err)
        }</span>

        // 保存配置到应用实例
        <span class="cov0" title="0">app.Config = config

        // 初始化日志记录器
        logger, err := initLogger(config)
        if err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("初始化日志记录器失败: %w", err)
        }</span>
        <span class="cov0" title="0">app.Logger = logger

        // 初始化数据库连接
        db, err := initDatabase(config)
        if err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("初始化数据库连接失败: %w", err)
        }</span>
        <span class="cov0" title="0">app.DB = db

        // 初始化 HTTP 服务器
        server, err := initServer(config)
        if err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("初始化 HTTP 服务器失败: %w", err)
        }</span>
        <span class="cov0" title="0">app.Server = server

        // 初始化认证服务
        authService := NewAuthService(db, &amp;config.Auth)
        app.Auth = authService

        // 创建用户表（如果不存在）
        if err := CreateUserTables(db); err != nil </span><span class="cov0" title="0">{
                app.Logger.Warn("创建用户表失败", "error", err)
        }</span>

        // 记录初始化成功信息
        <span class="cov0" title="0">app.Logger.Info("应用初始化成功",
                "config_file", configfile,
                "log_level", config.Log.Level,
                "log_output", func() string </span><span class="cov0" title="0">{
                        if config.Log.FilePath == "" </span><span class="cov0" title="0">{
                                return "标准输出"
                        }</span>
                        <span class="cov0" title="0">return config.Log.FilePath</span>
                }(),
                "database_driver", config.Database.Driver,
                "database_name", config.Database.Database,
                "server_framework", config.Server.Framework,
                "server_address", fmt.Sprintf("%s:%d", config.Server.Host, config.Server.Port))

        <span class="cov0" title="0">return nil</span>
}

// GetApp 获取应用实例
func GetApp() *Applicant <span class="cov0" title="0">{
        return app
}</span>

// GetLogger 获取日志记录器
func GetLogger() *slog.Logger <span class="cov0" title="0">{
        if app != nil </span><span class="cov0" title="0">{
                return app.Logger
        }</span>
        <span class="cov0" title="0">return nil</span>
}

// GetConfig 获取配置
func GetConfig() *Config <span class="cov0" title="0">{
        if app != nil </span><span class="cov0" title="0">{
                return app.Config
        }</span>
        <span class="cov0" title="0">return nil</span>
}

// GetDB 获取数据库连接
func GetDB() *sql.DB <span class="cov0" title="0">{
        if app != nil </span><span class="cov0" title="0">{
                return app.DB
        }</span>
        <span class="cov0" title="0">return nil</span>
}

// GetServer 获取 HTTP 服务器
func GetServer() ServerInterface <span class="cov0" title="0">{
        if app != nil </span><span class="cov0" title="0">{
                return app.Server
        }</span>
        <span class="cov0" title="0">return nil</span>
}

// GetAuth 获取认证服务
func GetAuth() *AuthService <span class="cov0" title="0">{
        if app != nil </span><span class="cov0" title="0">{
                return app.Auth
        }</span>
        <span class="cov0" title="0">return nil</span>
}

// Close 关闭应用资源
func Close() error <span class="cov0" title="0">{
        if app != nil </span><span class="cov0" title="0">{
                // 关闭 HTTP 服务器
                if app.Server != nil </span><span class="cov0" title="0">{
                        ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
                        defer cancel()

                        if err := app.Server.Stop(ctx); err != nil </span><span class="cov0" title="0">{
                                if app.Logger != nil </span><span class="cov0" title="0">{
                                        app.Logger.Error("关闭 HTTP 服务器失败", "error", err)
                                }</span>
                                <span class="cov0" title="0">return fmt.Errorf("关闭 HTTP 服务器失败: %w", err)</span>
                        }
                }

                // 关闭数据库连接
                <span class="cov0" title="0">if app.DB != nil </span><span class="cov0" title="0">{
                        if err := closeDatabase(app.DB); err != nil </span><span class="cov0" title="0">{
                                if app.Logger != nil </span><span class="cov0" title="0">{
                                        app.Logger.Error("关闭数据库连接失败", "error", err)
                                }</span>
                                <span class="cov0" title="0">return fmt.Errorf("关闭数据库连接失败: %w", err)</span>
                        }
                }

                <span class="cov0" title="0">if app.Logger != nil </span><span class="cov0" title="0">{
                        app.Logger.Info("应用资源已清理")
                }</span>
        }
        <span class="cov0" title="0">return nil</span>
}
</pre>
		
		<pre class="file" id="file1" style="display: none">// auth.go - 认证相关功能
// 负责JWT令牌管理、会话管理和认证中间件

package app

import (
        "database/sql"
        "fmt"
        "net/http"
        "strconv"
        "strings"
        "time"

        "github.com/gin-gonic/gin"
        "github.com/gofiber/fiber/v2"
        "github.com/golang-jwt/jwt/v5"
)

// AuthService 认证服务结构体
type AuthService struct {
        db     *sql.DB
        config *AuthConfig
        logger interface{} // 可以是 *slog.Logger
}

// JWTClaims JWT 声明结构体
type JWTClaims struct {
        UserID   int    `json:"user_id"`
        Username string `json:"username"`
        Role     string `json:"role"`
        jwt.RegisteredClaims
}

// LoginResponse 登录响应结构体
type LoginResponse struct {
        Token     string        `json:"token"`
        ExpiresAt time.Time     `json:"expires_at"`
        User      *UserResponse `json:"user"`
}

// NewAuthService 创建认证服务实例
func NewAuthService(db *sql.DB, config *AuthConfig) *AuthService <span class="cov0" title="0">{
        return &amp;AuthService{
                db:     db,
                config: config,
        }
}</span>

// GenerateJWT 生成JWT令牌
func (a *AuthService) GenerateJWT(user *User) (string, time.Time, error) <span class="cov0" title="0">{
        expiresAt := time.Now().Add(time.Duration(a.config.TokenExpireHours) * time.Hour)

        claims := &amp;JWTClaims{
                UserID:   user.ID,
                Username: user.Username,
                Role:     user.Role,
                RegisteredClaims: jwt.RegisteredClaims{
                        ExpiresAt: jwt.NewNumericDate(expiresAt),
                        IssuedAt:  jwt.NewNumericDate(time.Now()),
                        Issuer:    "pixiu-auth",
                },
        }

        token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
        tokenString, err := token.SignedString([]byte(a.config.JWTSecret))
        if err != nil </span><span class="cov0" title="0">{
                return "", time.Time{}, fmt.Errorf("生成JWT令牌失败: %w", err)
        }</span>

        <span class="cov0" title="0">return tokenString, expiresAt, nil</span>
}

// ValidateJWT 验证JWT令牌
func (a *AuthService) ValidateJWT(tokenString string) (*JWTClaims, error) <span class="cov0" title="0">{
        token, err := jwt.ParseWithClaims(tokenString, &amp;JWTClaims{}, func(token *jwt.Token) (interface{}, error) </span><span class="cov0" title="0">{
                if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok </span><span class="cov0" title="0">{
                        return nil, fmt.Errorf("意外的签名方法: %v", token.Header["alg"])
                }</span>
                <span class="cov0" title="0">return []byte(a.config.JWTSecret), nil</span>
        })

        <span class="cov0" title="0">if err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("解析JWT令牌失败: %w", err)
        }</span>

        <span class="cov0" title="0">if claims, ok := token.Claims.(*JWTClaims); ok &amp;&amp; token.Valid </span><span class="cov0" title="0">{
                return claims, nil
        }</span>

        <span class="cov0" title="0">return nil, fmt.Errorf("无效的JWT令牌")</span>
}

// CreateSession 创建用户会话
func (a *AuthService) CreateSession(userID int, token string) error <span class="cov0" title="0">{
        expiresAt := time.Now().Add(time.Duration(a.config.SessionExpireHours) * time.Hour)

        _, err := a.db.Exec(`
                INSERT INTO user_sessions (user_id, token, expires_at, created_at)
                VALUES (?, ?, ?, ?)`,
                userID, token, expiresAt, time.Now())

        if err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("创建会话失败: %w", err)
        }</span>

        <span class="cov0" title="0">return nil</span>
}

// ValidateSession 验证会话
func (a *AuthService) ValidateSession(token string) (*UserSession, error) <span class="cov0" title="0">{
        session := &amp;UserSession{}
        err := a.db.QueryRow(`
                SELECT id, user_id, token, expires_at, created_at
                FROM user_sessions 
                WHERE token = ? AND expires_at &gt; ?`,
                token, time.Now()).Scan(
                &amp;session.ID, &amp;session.UserID, &amp;session.Token,
                &amp;session.ExpiresAt, &amp;session.CreatedAt)

        if err != nil </span><span class="cov0" title="0">{
                if err == sql.ErrNoRows </span><span class="cov0" title="0">{
                        return nil, fmt.Errorf("会话不存在或已过期")
                }</span>
                <span class="cov0" title="0">return nil, fmt.Errorf("验证会话失败: %w", err)</span>
        }

        <span class="cov0" title="0">return session, nil</span>
}

// DeleteSession 删除会话
func (a *AuthService) DeleteSession(token string) error <span class="cov0" title="0">{
        _, err := a.db.Exec("DELETE FROM user_sessions WHERE token = ?", token)
        if err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("删除会话失败: %w", err)
        }</span>
        <span class="cov0" title="0">return nil</span>
}

// CleanExpiredSessions 清理过期会话
func (a *AuthService) CleanExpiredSessions() error <span class="cov0" title="0">{
        _, err := a.db.Exec("DELETE FROM user_sessions WHERE expires_at &lt; ?", time.Now())
        if err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("清理过期会话失败: %w", err)
        }</span>
        <span class="cov0" title="0">return nil</span>
}

// Login 用户登录
func (a *AuthService) Login(req *LoginRequest) (*LoginResponse, error) <span class="cov0" title="0">{
        // 获取用户
        user, err := GetUserByUsername(a.db, req.Username)
        if err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("用户名或密码错误")
        }</span>

        // 检查用户是否激活
        <span class="cov0" title="0">if !user.IsActive </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("用户账户已被禁用")
        }</span>

        // 验证密码
        <span class="cov0" title="0">if !CheckPassword(req.Password, user.PasswordHash) </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("用户名或密码错误")
        }</span>

        // 生成JWT令牌
        <span class="cov0" title="0">token, expiresAt, err := a.GenerateJWT(user)
        if err != nil </span><span class="cov0" title="0">{
                return nil, err
        }</span>

        // 创建会话
        <span class="cov0" title="0">if err := a.CreateSession(user.ID, token); err != nil </span><span class="cov0" title="0">{
                return nil, err
        }</span>

        <span class="cov0" title="0">return &amp;LoginResponse{
                Token:     token,
                ExpiresAt: expiresAt,
                User:      user.ToResponse(),
        }, nil</span>
}

// Logout 用户登出
func (a *AuthService) Logout(token string) error <span class="cov0" title="0">{
        return a.DeleteSession(token)
}</span>

// GetCurrentUser 获取当前用户信息
func (a *AuthService) GetCurrentUser(token string) (*User, error) <span class="cov0" title="0">{
        // 验证JWT令牌
        claims, err := a.ValidateJWT(token)
        if err != nil </span><span class="cov0" title="0">{
                return nil, err
        }</span>

        // 验证会话
        <span class="cov0" title="0">_, err = a.ValidateSession(token)
        if err != nil </span><span class="cov0" title="0">{
                return nil, err
        }</span>

        // 获取用户信息
        <span class="cov0" title="0">return GetUserByID(a.db, claims.UserID)</span>
}

// extractTokenFromHeader 从请求头中提取令牌
func extractTokenFromHeader(authHeader string) string <span class="cov0" title="0">{
        if authHeader == "" </span><span class="cov0" title="0">{
                return ""
        }</span>

        <span class="cov0" title="0">parts := strings.SplitN(authHeader, " ", 2)
        if len(parts) != 2 || parts[0] != "Bearer" </span><span class="cov0" title="0">{
                return ""
        }</span>

        <span class="cov0" title="0">return parts[1]</span>
}

// GinAuthMiddleware Gin 框架认证中间件
func (a *AuthService) GinAuthMiddleware() gin.HandlerFunc <span class="cov0" title="0">{
        return func(c *gin.Context) </span><span class="cov0" title="0">{
                token := extractTokenFromHeader(c.GetHeader("Authorization"))
                if token == "" </span><span class="cov0" title="0">{
                        c.JSON(http.StatusUnauthorized, gin.H{"error": "缺少认证令牌"})
                        c.Abort()
                        return
                }</span>

                <span class="cov0" title="0">user, err := a.GetCurrentUser(token)
                if err != nil </span><span class="cov0" title="0">{
                        c.JSON(http.StatusUnauthorized, gin.H{"error": "无效的认证令牌"})
                        c.Abort()
                        return
                }</span>

                // 将用户信息存储到上下文中
                <span class="cov0" title="0">c.Set("user", user)
                c.Set("user_id", user.ID)
                c.Set("username", user.Username)
                c.Set("role", user.Role)

                c.Next()</span>
        }
}

// GinRoleMiddleware Gin 框架角色权限中间件
func (a *AuthService) GinRoleMiddleware(requiredRoles ...string) gin.HandlerFunc <span class="cov0" title="0">{
        return func(c *gin.Context) </span><span class="cov0" title="0">{
                user, exists := c.Get("user")
                if !exists </span><span class="cov0" title="0">{
                        c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未认证"})
                        c.Abort()
                        return
                }</span>

                <span class="cov0" title="0">userObj := user.(*User)
                for _, role := range requiredRoles </span><span class="cov0" title="0">{
                        if userObj.Role == role </span><span class="cov0" title="0">{
                                c.Next()
                                return
                        }</span>
                }

                <span class="cov0" title="0">c.JSON(http.StatusForbidden, gin.H{"error": "权限不足"})
                c.Abort()</span>
        }
}

// FiberAuthMiddleware Fiber 框架认证中间件
func (a *AuthService) FiberAuthMiddleware() fiber.Handler <span class="cov0" title="0">{
        return func(c *fiber.Ctx) error </span><span class="cov0" title="0">{
                token := extractTokenFromHeader(c.Get("Authorization"))
                if token == "" </span><span class="cov0" title="0">{
                        return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
                                "error": "缺少认证令牌",
                        })
                }</span>

                <span class="cov0" title="0">user, err := a.GetCurrentUser(token)
                if err != nil </span><span class="cov0" title="0">{
                        return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
                                "error": "无效的认证令牌",
                        })
                }</span>

                // 将用户信息存储到上下文中
                <span class="cov0" title="0">c.Locals("user", user)
                c.Locals("user_id", user.ID)
                c.Locals("username", user.Username)
                c.Locals("role", user.Role)

                return c.Next()</span>
        }
}

// FiberRoleMiddleware Fiber 框架角色权限中间件
func (a *AuthService) FiberRoleMiddleware(requiredRoles ...string) fiber.Handler <span class="cov0" title="0">{
        return func(c *fiber.Ctx) error </span><span class="cov0" title="0">{
                user := c.Locals("user")
                if user == nil </span><span class="cov0" title="0">{
                        return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
                                "error": "用户未认证",
                        })
                }</span>

                <span class="cov0" title="0">userObj := user.(*User)
                for _, role := range requiredRoles </span><span class="cov0" title="0">{
                        if userObj.Role == role </span><span class="cov0" title="0">{
                                return c.Next()
                        }</span>
                }

                <span class="cov0" title="0">return c.Status(fiber.StatusForbidden).JSON(fiber.Map{
                        "error": "权限不足",
                })</span>
        }
}

// GetUserFromGinContext 从 Gin 上下文中获取用户
func GetUserFromGinContext(c *gin.Context) (*User, bool) <span class="cov0" title="0">{
        user, exists := c.Get("user")
        if !exists </span><span class="cov0" title="0">{
                return nil, false
        }</span>
        <span class="cov0" title="0">return user.(*User), true</span>
}

// GetUserFromFiberContext 从 Fiber 上下文中获取用户
func GetUserFromFiberContext(c *fiber.Ctx) (*User, bool) <span class="cov0" title="0">{
        user := c.Locals("user")
        if user == nil </span><span class="cov0" title="0">{
                return nil, false
        }</span>
        <span class="cov0" title="0">return user.(*User), true</span>
}

// GetUserIDFromString 从字符串转换用户ID
func GetUserIDFromString(idStr string) (int, error) <span class="cov0" title="0">{
        id, err := strconv.Atoi(idStr)
        if err != nil </span><span class="cov0" title="0">{
                return 0, fmt.Errorf("无效的用户ID")
        }</span>
        <span class="cov0" title="0">return id, nil</span>
}
</pre>
		
		<pre class="file" id="file2" style="display: none">// config.go - 配置相关功能
// 负责应用配置的定义、读取和解析

package app

import (
        "fmt"
        "os"

        "github.com/BurntSushi/toml"
)

// LogConfig 日志配置结构体
type LogConfig struct {
        Level    string `toml:"level"`     // 日志级别：debug, info, warn, error
        FilePath string `toml:"file_path"` // 日志文件路径，为空则输出到标准输出
}

// DatabaseConfig 数据库配置结构体
type DatabaseConfig struct {
        Driver          string `toml:"driver"`             // 数据库驱动：mysql, postgres, sqlite3
        Host            string `toml:"host"`               // 数据库主机地址
        Port            int    `toml:"port"`               // 数据库端口
        Database        string `toml:"database"`           // 数据库名称
        Username        string `toml:"username"`           // 用户名
        Password        string `toml:"password"`           // 密码
        SSLMode         string `toml:"ssl_mode"`           // SSL模式（postgres）
        Charset         string `toml:"charset"`            // 字符集（mysql）
        MaxOpenConns    int    `toml:"max_open_conns"`     // 最大打开连接数
        MaxIdleConns    int    `toml:"max_idle_conns"`     // 最大空闲连接数
        ConnMaxLifetime int    `toml:"conn_max_lifetime"`  // 连接最大生存时间（秒）
        ConnMaxIdleTime int    `toml:"conn_max_idle_time"` // 连接最大空闲时间（秒）
}

// ServerConfig HTTP 服务器配置结构体
type ServerConfig struct {
        Host         string `toml:"host"`          // 服务器主机地址
        Port         int    `toml:"port"`          // 服务器端口
        ReadTimeout  int    `toml:"read_timeout"`  // 读取超时（秒）
        WriteTimeout int    `toml:"write_timeout"` // 写入超时（秒）
        Framework    string `toml:"framework"`     // HTTP 框架：gin, fiber, net/http
}

// AuthConfig 认证配置结构体
type AuthConfig struct {
        JWTSecret          string `toml:"jwt_secret"`           // JWT 签名密钥
        TokenExpireHours   int    `toml:"token_expire_hours"`   // 令牌过期时间（小时）
        SessionExpireHours int    `toml:"session_expire_hours"` // 会话过期时间（小时）
        PasswordMinLength  int    `toml:"password_min_length"`  // 密码最小长度
        RequireSpecialChar bool   `toml:"require_special_char"` // 是否要求特殊字符
        MaxLoginAttempts   int    `toml:"max_login_attempts"`   // 最大登录尝试次数
        LockoutDurationMin int    `toml:"lockout_duration_min"` // 锁定持续时间（分钟）
}

// Config 应用配置结构体
type Config struct {
        Log      LogConfig      `toml:"log"`      // 日志配置
        Database DatabaseConfig `toml:"database"` // 数据库配置
        Server   ServerConfig   `toml:"server"`   // 服务器配置
        Auth     AuthConfig     `toml:"auth"`     // 认证配置
}

// newDefaultConfig 创建默认配置
func newDefaultConfig() *Config <span class="cov8" title="1">{
        return &amp;Config{
                Log: LogConfig{
                        Level:    "info",
                        FilePath: "", // 默认输出到标准输出
                },
                Database: DatabaseConfig{
                        Driver:          "sqlite", // 默认使用 SQLite (glebarez/sqlite)
                        Host:            "localhost",
                        Port:            0,          // SQLite 不需要端口
                        Database:        "pixiu.db", // 默认数据库文件
                        Username:        "",
                        Password:        "",
                        SSLMode:         "disable",
                        Charset:         "utf8mb4",
                        MaxOpenConns:    25,  // 默认最大打开连接数
                        MaxIdleConns:    5,   // 默认最大空闲连接数
                        ConnMaxLifetime: 300, // 5分钟
                        ConnMaxIdleTime: 60,  // 1分钟
                },
                Server: ServerConfig{
                        Host:         "localhost", // 默认主机地址
                        Port:         8080,        // 默认端口
                        ReadTimeout:  30,          // 30秒读取超时
                        WriteTimeout: 30,          // 30秒写入超时
                        Framework:    "gin",       // 默认使用 Gin 框架
                },
                Auth: AuthConfig{
                        JWTSecret:          "pixiu-default-secret-change-in-production", // 默认JWT密钥
                        TokenExpireHours:   24,                                          // 24小时令牌过期
                        SessionExpireHours: 168,                                         // 7天会话过期
                        PasswordMinLength:  8,                                           // 最小密码长度8位
                        RequireSpecialChar: true,                                        // 要求特殊字符
                        MaxLoginAttempts:   5,                                           // 最大登录尝试5次
                        LockoutDurationMin: 15,                                          // 锁定15分钟
                },
        }
}</span>

// loadConfig 读取并解析配置文件
// 如果 configFile 为空，则返回默认配置
// 如果配置文件不存在或解析失败，返回错误
func loadConfig(configFile string) (*Config, error) <span class="cov0" title="0">{
        // 创建默认配置
        config := newDefaultConfig()

        // 如果没有指定配置文件，使用默认配置
        if configFile == "" </span><span class="cov0" title="0">{
                return config, nil
        }</span>

        // 检查配置文件是否存在
        <span class="cov0" title="0">if _, err := os.Stat(configFile); err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("配置文件不存在: %s, 错误: %w", configFile, err)
        }</span>

        // 解析配置文件
        <span class="cov0" title="0">if _, err := toml.DecodeFile(configFile, config); err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("解析配置文件失败: %w", err)
        }</span>

        <span class="cov0" title="0">return config, nil</span>
}
</pre>
		
		<pre class="file" id="file3" style="display: none">// database.go - 数据库相关功能
// 负责数据库连接的初始化、配置和管理

package app

import (
        "context"
        "database/sql"
        "fmt"
        "time"

        // 导入数据库驱动
        _ "github.com/glebarez/sqlite"
        // 其他驱动可按需添加：
        // _ "github.com/go-sql-driver/mysql"
        // _ "github.com/lib/pq"
)

// buildConnectionString 根据配置构建数据库连接字符串
func buildConnectionString(config *DatabaseConfig) (string, error) <span class="cov0" title="0">{
        switch config.Driver </span>{
        case "mysql":<span class="cov0" title="0">
                // MySQL 连接字符串格式: username:password@tcp(host:port)/database?charset=utf8mb4&amp;parseTime=True&amp;loc=Local
                dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=%s&amp;parseTime=True&amp;loc=Local",
                        config.Username, config.Password, config.Host, config.Port, config.Database, config.Charset)
                return dsn, nil</span>

        case "postgres", "postgresql":<span class="cov0" title="0">
                // PostgreSQL 连接字符串格式: host=localhost port=5432 user=username password=password dbname=database sslmode=disable
                dsn := fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=%s",
                        config.Host, config.Port, config.Username, config.Password, config.Database, config.SSLMode)
                return dsn, nil</span>

        case "sqlite":<span class="cov0" title="0">
                // SQLite 连接字符串格式: database.db 或 file:database.db?options
                // glebarez/sqlite 支持简单的文件路径格式
                dsn := config.Database
                if dsn == "" </span><span class="cov0" title="0">{
                        dsn = "pixiu.db" // 默认数据库文件名
                }</span>
                <span class="cov0" title="0">return dsn, nil</span>

        default:<span class="cov0" title="0">
                return "", fmt.Errorf("不支持的数据库驱动: %s", config.Driver)</span>
        }
}

// initDatabase 初始化数据库连接
func initDatabase(config *Config) (*sql.DB, error) <span class="cov0" title="0">{
        dbConfig := &amp;config.Database

        // 构建连接字符串
        dsn, err := buildConnectionString(dbConfig)
        if err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("构建数据库连接字符串失败: %w", err)
        }</span>

        // 打开数据库连接
        <span class="cov0" title="0">db, err := sql.Open(dbConfig.Driver, dsn)
        if err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("打开数据库连接失败: %w", err)
        }</span>

        // 配置连接池
        <span class="cov0" title="0">if dbConfig.MaxOpenConns &gt; 0 </span><span class="cov0" title="0">{
                db.SetMaxOpenConns(dbConfig.MaxOpenConns)
        }</span>
        <span class="cov0" title="0">if dbConfig.MaxIdleConns &gt; 0 </span><span class="cov0" title="0">{
                db.SetMaxIdleConns(dbConfig.MaxIdleConns)
        }</span>
        <span class="cov0" title="0">if dbConfig.ConnMaxLifetime &gt; 0 </span><span class="cov0" title="0">{
                db.SetConnMaxLifetime(time.Duration(dbConfig.ConnMaxLifetime) * time.Second)
        }</span>
        <span class="cov0" title="0">if dbConfig.ConnMaxIdleTime &gt; 0 </span><span class="cov0" title="0">{
                db.SetConnMaxIdleTime(time.Duration(dbConfig.ConnMaxIdleTime) * time.Second)
        }</span>

        // 测试数据库连接
        <span class="cov0" title="0">if err := testDatabaseConnection(db); err != nil </span><span class="cov0" title="0">{
                db.Close()
                return nil, fmt.Errorf("数据库连接测试失败: %w", err)
        }</span>

        <span class="cov0" title="0">return db, nil</span>
}

// testDatabaseConnection 测试数据库连接
func testDatabaseConnection(db *sql.DB) error <span class="cov0" title="0">{
        ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
        defer cancel()

        if err := db.PingContext(ctx); err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("数据库连接测试失败: %w", err)
        }</span>

        <span class="cov0" title="0">return nil</span>
}

// closeDatabase 关闭数据库连接
func closeDatabase(db *sql.DB) error <span class="cov0" title="0">{
        if db != nil </span><span class="cov0" title="0">{
                return db.Close()
        }</span>
        <span class="cov0" title="0">return nil</span>
}
</pre>
		
		<pre class="file" id="file4" style="display: none">// logger.go - 日志相关功能
// 负责日志记录器的初始化、配置和管理

package app

import (
        "fmt"
        "io"
        "log/slog"
        "os"
        "path/filepath"
        "strings"
)

// parseLogLevel 将字符串转换为 slog.Level
func parseLogLevel(level string) slog.Level <span class="cov0" title="0">{
        switch strings.ToLower(level) </span>{
        case "debug":<span class="cov0" title="0">
                return slog.LevelDebug</span>
        case "info":<span class="cov0" title="0">
                return slog.LevelInfo</span>
        case "warn", "warning":<span class="cov0" title="0">
                return slog.LevelWarn</span>
        case "error":<span class="cov0" title="0">
                return slog.LevelError</span>
        default:<span class="cov0" title="0">
                return slog.LevelInfo</span> // 默认级别
        }
}

// createLogWriter 根据配置创建日志输出目标
func createLogWriter(filePath string) (io.Writer, error) <span class="cov0" title="0">{
        if filePath == "" </span><span class="cov0" title="0">{
                return os.Stdout, nil
        }</span>

        // 确保日志文件目录存在
        <span class="cov0" title="0">dir := filepath.Dir(filePath)
        if err := os.MkdirAll(dir, 0755); err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("创建日志目录失败: %w", err)
        }</span>

        // 创建或打开日志文件
        <span class="cov0" title="0">file, err := os.OpenFile(filePath, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
        if err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("创建日志文件失败: %w", err)
        }</span>

        <span class="cov0" title="0">return file, nil</span>
}

// initLogger 根据配置初始化日志记录器
func initLogger(config *Config) (*slog.Logger, error) <span class="cov0" title="0">{
        // 根据配置创建日志输出目标
        logWriter, err := createLogWriter(config.Log.FilePath)
        if err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("创建日志输出目标失败: %w", err)
        }</span>

        // 配置日志选项
        <span class="cov0" title="0">opts := &amp;slog.HandlerOptions{
                Level: parseLogLevel(config.Log.Level),
        }

        // 初始化文本格式的日志处理器
        handler := slog.NewTextHandler(logWriter, opts)
        logger := slog.New(handler)

        return logger, nil</span>
}
</pre>
		
		<pre class="file" id="file5" style="display: none">// server.go - HTTP 服务器相关功能
// 负责 HTTP 服务器的初始化、配置和管理

package app

import (
        "context"
        "fmt"
        "net/http"
        "time"

        "github.com/gin-gonic/gin"
        "github.com/gofiber/fiber/v2"
        "github.com/gofiber/fiber/v2/middleware/logger"
        "github.com/gofiber/fiber/v2/middleware/recover"
)

// ServerInterface 服务器接口，支持不同的 HTTP 框架
type ServerInterface interface {
        Start() error
        Stop(ctx context.Context) error
        GetEngine() interface{}
}

// GinServer Gin 框架服务器实现
type GinServer struct {
        engine *gin.Engine
        server *http.Server
        config *ServerConfig
}

// NewGinServer 创建 Gin 服务器实例
func NewGinServer(config *ServerConfig) *GinServer <span class="cov0" title="0">{
        // 设置 Gin 模式
        gin.SetMode(gin.ReleaseMode)

        engine := gin.New()

        // 添加中间件
        engine.Use(gin.Logger())
        engine.Use(gin.Recovery())

        // 创建 HTTP 服务器
        server := &amp;http.Server{
                Addr:         fmt.Sprintf("%s:%d", config.Host, config.Port),
                Handler:      engine,
                ReadTimeout:  time.Duration(config.ReadTimeout) * time.Second,
                WriteTimeout: time.Duration(config.WriteTimeout) * time.Second,
        }

        return &amp;GinServer{
                engine: engine,
                server: server,
                config: config,
        }
}</span>

// Start 启动服务器
func (s *GinServer) Start() error <span class="cov0" title="0">{
        return s.server.ListenAndServe()
}</span>

// Stop 停止服务器
func (s *GinServer) Stop(ctx context.Context) error <span class="cov0" title="0">{
        return s.server.Shutdown(ctx)
}</span>

// GetEngine 获取 Gin 引擎
func (s *GinServer) GetEngine() interface{} <span class="cov0" title="0">{
        return s.engine
}</span>

// NetHTTPServer 标准库 HTTP 服务器实现
type NetHTTPServer struct {
        mux    *http.ServeMux
        server *http.Server
        config *ServerConfig
}

// NewNetHTTPServer 创建标准库 HTTP 服务器实例
func NewNetHTTPServer(config *ServerConfig) *NetHTTPServer <span class="cov0" title="0">{
        mux := http.NewServeMux()

        server := &amp;http.Server{
                Addr:         fmt.Sprintf("%s:%d", config.Host, config.Port),
                Handler:      mux,
                ReadTimeout:  time.Duration(config.ReadTimeout) * time.Second,
                WriteTimeout: time.Duration(config.WriteTimeout) * time.Second,
        }

        return &amp;NetHTTPServer{
                mux:    mux,
                server: server,
                config: config,
        }
}</span>

// Start 启动服务器
func (s *NetHTTPServer) Start() error <span class="cov0" title="0">{
        return s.server.ListenAndServe()
}</span>

// Stop 停止服务器
func (s *NetHTTPServer) Stop(ctx context.Context) error <span class="cov0" title="0">{
        return s.server.Shutdown(ctx)
}</span>

// GetEngine 获取 HTTP 多路复用器
func (s *NetHTTPServer) GetEngine() interface{} <span class="cov0" title="0">{
        return s.mux
}</span>

// FiberServer Fiber 框架服务器实现
type FiberServer struct {
        app    *fiber.App
        config *ServerConfig
}

// NewFiberServer 创建 Fiber 服务器实例
func NewFiberServer(config *ServerConfig) *FiberServer <span class="cov0" title="0">{
        // 创建 Fiber 应用配置
        fiberConfig := fiber.Config{
                ReadTimeout:  time.Duration(config.ReadTimeout) * time.Second,
                WriteTimeout: time.Duration(config.WriteTimeout) * time.Second,
                ServerHeader: "Pixiu-Fiber",
                AppName:      "Pixiu v1.0.0",
        }

        // 创建 Fiber 应用
        app := fiber.New(fiberConfig)

        // 添加中间件
        app.Use(logger.New())
        app.Use(recover.New())

        return &amp;FiberServer{
                app:    app,
                config: config,
        }
}</span>

// Start 启动服务器
func (s *FiberServer) Start() error <span class="cov0" title="0">{
        addr := fmt.Sprintf("%s:%d", s.config.Host, s.config.Port)
        return s.app.Listen(addr)
}</span>

// Stop 停止服务器
func (s *FiberServer) Stop(ctx context.Context) error <span class="cov0" title="0">{
        return s.app.ShutdownWithContext(ctx)
}</span>

// GetEngine 获取 Fiber 应用
func (s *FiberServer) GetEngine() interface{} <span class="cov0" title="0">{
        return s.app
}</span>

// initServer 根据配置初始化 HTTP 服务器
func initServer(config *Config) (ServerInterface, error) <span class="cov0" title="0">{
        serverConfig := &amp;config.Server

        switch serverConfig.Framework </span>{
        case "gin":<span class="cov0" title="0">
                return NewGinServer(serverConfig), nil</span>
        case "fiber":<span class="cov0" title="0">
                return NewFiberServer(serverConfig), nil</span>
        case "net/http":<span class="cov0" title="0">
                return NewNetHTTPServer(serverConfig), nil</span>
        default:<span class="cov0" title="0">
                return nil, fmt.Errorf("不支持的 HTTP 框架: %s", serverConfig.Framework)</span>
        }
}
</pre>
		
		<pre class="file" id="file6" style="display: none">// user.go - 用户管理相关功能
// 负责用户数据模型、CRUD操作和数据库迁移

package app

import (
        "database/sql"
        "fmt"
        "regexp"
        "strings"
        "time"

        "golang.org/x/crypto/bcrypt"
)

// User 用户数据模型
type User struct {
        ID           int       `json:"id" db:"id"`
        Username     string    `json:"username" db:"username"`
        Email        string    `json:"email" db:"email"`
        PasswordHash string    `json:"-" db:"password_hash"` // 不在JSON中显示
        Salt         string    `json:"-" db:"salt"`          // 不在JSON中显示
        CreatedAt    time.Time `json:"created_at" db:"created_at"`
        UpdatedAt    time.Time `json:"updated_at" db:"updated_at"`
        IsActive     bool      `json:"is_active" db:"is_active"`
        Role         string    `json:"role" db:"role"`
}

// UserSession 用户会话数据模型
type UserSession struct {
        ID        int       `json:"id" db:"id"`
        UserID    int       `json:"user_id" db:"user_id"`
        Token     string    `json:"token" db:"token"`
        ExpiresAt time.Time `json:"expires_at" db:"expires_at"`
        CreatedAt time.Time `json:"created_at" db:"created_at"`
}

// CreateUserRequest 创建用户请求结构
type CreateUserRequest struct {
        Username string `json:"username" binding:"required,min=3,max=50"`
        Email    string `json:"email" binding:"required,email"`
        Password string `json:"password" binding:"required,min=8"`
        Role     string `json:"role,omitempty"`
}

// LoginRequest 登录请求结构
type LoginRequest struct {
        Username string `json:"username" binding:"required"`
        Password string `json:"password" binding:"required"`
}

// UpdateUserRequest 更新用户请求结构
type UpdateUserRequest struct {
        Email    string `json:"email,omitempty" binding:"omitempty,email"`
        IsActive *bool  `json:"is_active,omitempty"`
        Role     string `json:"role,omitempty"`
}

// ChangePasswordRequest 修改密码请求结构
type ChangePasswordRequest struct {
        OldPassword string `json:"old_password" binding:"required"`
        NewPassword string `json:"new_password" binding:"required,min=8"`
}

// UserResponse 用户响应结构（不包含敏感信息）
type UserResponse struct {
        ID        int       `json:"id"`
        Username  string    `json:"username"`
        Email     string    `json:"email"`
        CreatedAt time.Time `json:"created_at"`
        UpdatedAt time.Time `json:"updated_at"`
        IsActive  bool      `json:"is_active"`
        Role      string    `json:"role"`
}

// ToResponse 将 User 转换为 UserResponse
func (u *User) ToResponse() *UserResponse <span class="cov0" title="0">{
        return &amp;UserResponse{
                ID:        u.ID,
                Username:  u.Username,
                Email:     u.Email,
                CreatedAt: u.CreatedAt,
                UpdatedAt: u.UpdatedAt,
                IsActive:  u.IsActive,
                Role:      u.Role,
        }
}</span>

// ValidateEmail 验证邮箱格式
func ValidateEmail(email string) bool <span class="cov8" title="1">{
        emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
        return emailRegex.MatchString(email)
}</span>

// ValidatePassword 验证密码强度
func ValidatePassword(password string, config *AuthConfig) error <span class="cov0" title="0">{
        if len(password) &lt; config.PasswordMinLength </span><span class="cov0" title="0">{
                return fmt.Errorf("密码长度至少需要 %d 位", config.PasswordMinLength)
        }</span>

        <span class="cov0" title="0">if config.RequireSpecialChar </span><span class="cov0" title="0">{
                hasSpecial := regexp.MustCompile(`[!@#$%^&amp;*()_+\-=\[\]{};':"\\|,.&lt;&gt;\/?]`).MatchString(password)
                hasNumber := regexp.MustCompile(`[0-9]`).MatchString(password)
                hasUpper := regexp.MustCompile(`[A-Z]`).MatchString(password)
                hasLower := regexp.MustCompile(`[a-z]`).MatchString(password)

                if !hasSpecial || !hasNumber || !hasUpper || !hasLower </span><span class="cov0" title="0">{
                        return fmt.Errorf("密码必须包含大写字母、小写字母、数字和特殊字符")
                }</span>
        }

        <span class="cov0" title="0">return nil</span>
}

// HashPassword 加密密码
func HashPassword(password string) (string, error) <span class="cov8" title="1">{
        bytes, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
        return string(bytes), err
}</span>

// CheckPassword 验证密码
func CheckPassword(password, hash string) bool <span class="cov8" title="1">{
        err := bcrypt.CompareHashAndPassword([]byte(hash), []byte(password))
        return err == nil
}</span>

// CreateUserTables 创建用户相关表
func CreateUserTables(db *sql.DB) error <span class="cov0" title="0">{
        // 创建用户表
        userTableSQL := `
        CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username VARCHAR(50) UNIQUE NOT NULL,
                email VARCHAR(100) UNIQUE NOT NULL,
                password_hash VARCHAR(255) NOT NULL,
                salt VARCHAR(255) NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                is_active BOOLEAN DEFAULT TRUE,
                role VARCHAR(20) DEFAULT 'user'
        )`

        if _, err := db.Exec(userTableSQL); err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("创建用户表失败: %w", err)
        }</span>

        // 创建用户会话表
        <span class="cov0" title="0">sessionTableSQL := `
        CREATE TABLE IF NOT EXISTS user_sessions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                token VARCHAR(255) UNIQUE NOT NULL,
                expires_at DATETIME NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        )`

        if _, err := db.Exec(sessionTableSQL); err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("创建用户会话表失败: %w", err)
        }</span>

        // 创建索引
        <span class="cov0" title="0">indexes := []string{
                "CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)",
                "CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)",
                "CREATE INDEX IF NOT EXISTS idx_sessions_token ON user_sessions(token)",
                "CREATE INDEX IF NOT EXISTS idx_sessions_user_id ON user_sessions(user_id)",
                "CREATE INDEX IF NOT EXISTS idx_sessions_expires_at ON user_sessions(expires_at)",
        }

        for _, indexSQL := range indexes </span><span class="cov0" title="0">{
                if _, err := db.Exec(indexSQL); err != nil </span><span class="cov0" title="0">{
                        return fmt.Errorf("创建索引失败: %w", err)
                }</span>
        }

        <span class="cov0" title="0">return nil</span>
}

// CreateUser 创建新用户
func CreateUser(db *sql.DB, req *CreateUserRequest, config *AuthConfig) (*User, error) <span class="cov0" title="0">{
        // 验证输入
        if !ValidateEmail(req.Email) </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("邮箱格式无效")
        }</span>

        <span class="cov0" title="0">if err := ValidatePassword(req.Password, config); err != nil </span><span class="cov0" title="0">{
                return nil, err
        }</span>

        // 检查用户名和邮箱是否已存在
        <span class="cov0" title="0">var count int
        err := db.QueryRow("SELECT COUNT(*) FROM users WHERE username = ? OR email = ?",
                req.Username, req.Email).Scan(&amp;count)
        if err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("检查用户是否存在失败: %w", err)
        }</span>
        <span class="cov0" title="0">if count &gt; 0 </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("用户名或邮箱已存在")
        }</span>

        // 加密密码
        <span class="cov0" title="0">passwordHash, err := HashPassword(req.Password)
        if err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("密码加密失败: %w", err)
        }</span>

        // 设置默认角色
        <span class="cov0" title="0">role := req.Role
        if role == "" </span><span class="cov0" title="0">{
                role = "user"
        }</span>

        // 插入用户
        <span class="cov0" title="0">now := time.Now()
        result, err := db.Exec(`
                INSERT INTO users (username, email, password_hash, salt, created_at, updated_at, is_active, role)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
                req.Username, req.Email, passwordHash, "", now, now, true, role)

        if err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("创建用户失败: %w", err)
        }</span>

        <span class="cov0" title="0">userID, err := result.LastInsertId()
        if err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("获取用户ID失败: %w", err)
        }</span>

        // 返回创建的用户
        <span class="cov0" title="0">return GetUserByID(db, int(userID))</span>
}

// GetUserByID 根据ID获取用户
func GetUserByID(db *sql.DB, userID int) (*User, error) <span class="cov0" title="0">{
        user := &amp;User{}
        err := db.QueryRow(`
                SELECT id, username, email, password_hash, salt, created_at, updated_at, is_active, role
                FROM users WHERE id = ?`, userID).Scan(
                &amp;user.ID, &amp;user.Username, &amp;user.Email, &amp;user.PasswordHash, &amp;user.Salt,
                &amp;user.CreatedAt, &amp;user.UpdatedAt, &amp;user.IsActive, &amp;user.Role)

        if err != nil </span><span class="cov0" title="0">{
                if err == sql.ErrNoRows </span><span class="cov0" title="0">{
                        return nil, fmt.Errorf("用户不存在")
                }</span>
                <span class="cov0" title="0">return nil, fmt.Errorf("查询用户失败: %w", err)</span>
        }

        <span class="cov0" title="0">return user, nil</span>
}

// GetUserByUsername 根据用户名获取用户
func GetUserByUsername(db *sql.DB, username string) (*User, error) <span class="cov0" title="0">{
        user := &amp;User{}
        err := db.QueryRow(`
                SELECT id, username, email, password_hash, salt, created_at, updated_at, is_active, role
                FROM users WHERE username = ?`, username).Scan(
                &amp;user.ID, &amp;user.Username, &amp;user.Email, &amp;user.PasswordHash, &amp;user.Salt,
                &amp;user.CreatedAt, &amp;user.UpdatedAt, &amp;user.IsActive, &amp;user.Role)

        if err != nil </span><span class="cov0" title="0">{
                if err == sql.ErrNoRows </span><span class="cov0" title="0">{
                        return nil, fmt.Errorf("用户不存在")
                }</span>
                <span class="cov0" title="0">return nil, fmt.Errorf("查询用户失败: %w", err)</span>
        }

        <span class="cov0" title="0">return user, nil</span>
}

// UpdateUser 更新用户信息
func UpdateUser(db *sql.DB, userID int, req *UpdateUserRequest) (*User, error) <span class="cov0" title="0">{
        // 构建更新语句
        setParts := []string{}
        args := []interface{}{}

        if req.Email != "" </span><span class="cov0" title="0">{
                if !ValidateEmail(req.Email) </span><span class="cov0" title="0">{
                        return nil, fmt.Errorf("邮箱格式无效")
                }</span>
                <span class="cov0" title="0">setParts = append(setParts, "email = ?")
                args = append(args, req.Email)</span>
        }

        <span class="cov0" title="0">if req.IsActive != nil </span><span class="cov0" title="0">{
                setParts = append(setParts, "is_active = ?")
                args = append(args, *req.IsActive)
        }</span>

        <span class="cov0" title="0">if req.Role != "" </span><span class="cov0" title="0">{
                setParts = append(setParts, "role = ?")
                args = append(args, req.Role)
        }</span>

        <span class="cov0" title="0">if len(setParts) == 0 </span><span class="cov0" title="0">{
                return GetUserByID(db, userID) // 没有更新内容，直接返回原用户
        }</span>

        <span class="cov0" title="0">setParts = append(setParts, "updated_at = ?")
        args = append(args, time.Now())
        args = append(args, userID)

        updateSQL := fmt.Sprintf("UPDATE users SET %s WHERE id = ?", strings.Join(setParts, ", "))

        _, err := db.Exec(updateSQL, args...)
        if err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("更新用户失败: %w", err)
        }</span>

        <span class="cov0" title="0">return GetUserByID(db, userID)</span>
}

// ChangeUserPassword 修改用户密码
func ChangeUserPassword(db *sql.DB, userID int, req *ChangePasswordRequest, config *AuthConfig) error <span class="cov0" title="0">{
        // 获取用户当前密码
        user, err := GetUserByID(db, userID)
        if err != nil </span><span class="cov0" title="0">{
                return err
        }</span>

        // 验证旧密码
        <span class="cov0" title="0">if !CheckPassword(req.OldPassword, user.PasswordHash) </span><span class="cov0" title="0">{
                return fmt.Errorf("旧密码不正确")
        }</span>

        // 验证新密码
        <span class="cov0" title="0">if err := ValidatePassword(req.NewPassword, config); err != nil </span><span class="cov0" title="0">{
                return err
        }</span>

        // 加密新密码
        <span class="cov0" title="0">newPasswordHash, err := HashPassword(req.NewPassword)
        if err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("新密码加密失败: %w", err)
        }</span>

        // 更新密码
        <span class="cov0" title="0">_, err = db.Exec("UPDATE users SET password_hash = ?, updated_at = ? WHERE id = ?",
                newPasswordHash, time.Now(), userID)
        if err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("更新密码失败: %w", err)
        }</span>

        <span class="cov0" title="0">return nil</span>
}
</pre>
		
		</div>
	</body>
	<script>
	(function() {
		var files = document.getElementById('files');
		var visible;
		files.addEventListener('change', onChange, false);
		function select(part) {
			if (visible)
				visible.style.display = 'none';
			visible = document.getElementById(part);
			if (!visible)
				return;
			files.value = part;
			visible.style.display = 'block';
			location.hash = part;
		}
		function onChange() {
			select(files.value);
			window.scrollTo(0, 0);
		}
		if (location.hash != "") {
			select(location.hash.substr(1));
		}
		if (!visible) {
			select("file0");
		}
	})();
	</script>
</html>
